import { requestClient } from '#/api/request';

/**
 * 网关状态数据类型定义
 */
export interface GatewayStatusData {
  stationName: string; // 变配电站名称
  gateway: string; // 网关
  serialPort: string; // 串口号
  meterAddress: string; // 仪表地址
  meterName: string; // 仪表名称
  switchStatus: number; // 开关状态：1-开启，0-关闭
  commStatus: number; // 通讯状态：1-在线，0-离线
  offlineTime: number; // 离线时间（时间戳）
  lastCollectTime: number; // 最后采集时间（时间戳）
  totalInterruptTime: number; // 累计中断时间（时间戳）
}

/**
 * 网关状态查询参数
 */
export interface GatewayStatusQueryParams {
  enterpriseId?: string; // 企业ID
  siteId?: string; // 站点ID
  keyword?: string; // 关键字搜索
  commLossOnly?: boolean; // 只显示通讯中断
}

/**
 * 分页参数
 */
export interface PaginationParams {
  page: number; // 页码
  pageSize: number; // 每页数量
}

/**
 * 完整的请求参数结构
 */
export interface GatewayStatusRequestParams {
  query?: GatewayStatusQueryParams; // 查询参数（可选）
  pagination: PaginationParams; // 分页参数
}

/**
 * 网关状态响应数据
 */
export interface GatewayStatusResponse {
  items: GatewayStatusData[];
  total: number;
  page: number;
  pageSize: number;
}

/**
 * 获取网关状态数据
 * @param requestParams 请求参数（包含query和pagination）
 * @returns 网关状态数据
 */
export async function getGatewayStatusApi(
  requestParams: GatewayStatusRequestParams,
): Promise<GatewayStatusResponse> {
  return requestClient.post('/power/gateway-status', requestParams);
}
