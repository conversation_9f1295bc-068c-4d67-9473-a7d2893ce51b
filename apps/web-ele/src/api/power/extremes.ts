import { requestClient } from '#/api/request';

/**
 * 电力极值报表数据类型定义
 */
export interface PowerExtremesData {
  circuitName: string; // 回路名称
  date: number; // 日期时间戳
  // 有功功率(kW)
  activePowerMaxValue: number; // 最大值数值
  activePowerMaxTime: number; // 最大值发生时间戳
  activePowerMinValue: number; // 最小值数值
  activePowerMinTime: number; // 最小值发生时间戳
  activePowerAvgValue: number; // 平均值
  // 无功功率(kVar)
  reactivePowerMaxValue: number; // 最大值数值
  reactivePowerMaxTime: number; // 最大值发生时间戳
  reactivePowerMinValue: number; // 最小值数值
  reactivePowerMinTime: number; // 最小值发生时间戳
  reactivePowerAvgValue: number; // 平均值
  // 视在功率(kVA)
  apparentPowerMaxValue: number; // 最大值数值
  apparentPowerMaxTime: number; // 最大值发生时间戳
  apparentPowerMinValue: number; // 最小值数值
  apparentPowerMinTime: number; // 最小值发生时间戳
  apparentPowerAvgValue: number; // 平均值
  // 功率因数
  powerFactorMaxValue: number; // 最大值数值
  powerFactorMaxTime: number; // 最大值发生时间戳
  powerFactorMinValue: number; // 最小值数值
  powerFactorMinTime: number; // 最小值发生时间戳
  powerFactorAvgValue: number; // 平均值
}

/**
 * 电力极值报表查询参数（业务参数）
 */
export interface PowerExtremesQueryParams {
  startTime?: string; // 开始时间
  endTime?: string; // 结束时间
  reportType?: 'dayly' | 'monthly' | 'custom'; // 报表类型
  electricalCategory?: string; // 电力类别 (1:功率, 2:电流, 3:相电压, 4:线电压, 5:不平衡度, 6:电压谐波, 7:电流谐波)
  // 左侧筛选表单的参数
  enterpriseId?: string; // 企业ID
  energyType?: string; // 能源类型 (1:电, 2:气, 3:水等)
  siteId?: string; // 站点ID
  keyword?: string; // 关键字
  circuitNames?: string[]; // 选中的回路ID列表（复数形式）
}

/**
 * 分页参数
 */
export interface PaginationParams {
  page: number; // 页码
  pageSize: number; // 每页数量
}

/**
 * 完整的请求参数结构
 */
export interface PowerExtremesRequestParams {
  query: PowerExtremesQueryParams; // 查询参数
  pagination: PaginationParams; // 分页参数
}

/**
 * 电力极值报表响应数据
 */
export interface PowerExtremesResponse {
  items: PowerExtremesData[];
  total: number;
  page: number;
  pageSize: number;
  reportType?: string;
}

/**
 * 获取电力极值报表数据（POST方法）
 * @param requestParams 请求参数（包含query和pagination）
 * @returns 电力极值报表数据
 */
export async function getPowerExtremesApi(
  requestParams: PowerExtremesRequestParams,
): Promise<PowerExtremesResponse> {
  return requestClient.post('/power/extremes', requestParams);
}
