import { requestClient } from '#/api/request';

/**
 * 回路树节点数据类型定义
 */
export interface CircuitTreeNode {
  id: string;
  label: string; // 回路名称
  parentId?: string | null;
  level: number; // 节点层级：1-3
  type: 'group' | 'circuit'; // 节点类型：分组或回路
  status?: number; // 状态：1-启用，0-禁用
  sort?: number; // 排序
  children?: CircuitTreeNode[];
  created_at?: string;
  updated_at?: string;
}

/**
 * 回路树查询条件
 */
export interface CircuitTreeQuery {
  label?: string;
  type?: 'group' | 'circuit';
  level?: number;
  parentId?: string;
  status?: number;
  siteId?: string; // 站点ID，用于获取站点特定的回路配置
}

/**
 * 回路树请求参数
 */
export interface CircuitTreeParams {
  query?: CircuitTreeQuery;
}

/**
 * 回路树响应数据
 */
export interface CircuitTreeResponse {
  items: CircuitTreeNode[];
  total?: number;
}

/**
 * 获取回路树形数据
 * @param params 查询参数
 * @returns 回路树形数据
 */
export async function getCircuitTreeApi(
  params?: CircuitTreeParams,
): Promise<CircuitTreeResponse> {
  console.log('getCircuitTreeApi called with params:', params);

  return requestClient.get('/power/circuits/tree', {
    params,
  });
}

/**
 * 获取所有回路名称列表（扁平化）
 * @param params 查询参数
 * @returns 回路名称列表
 */
export async function getCircuitNamesApi(
  params?: { keyword?: string },
): Promise<{ label: string; value: string }[]> {
  console.log('getCircuitNamesApi called with params:', params);

  const response = await requestClient.get('/power/circuits/names', {
    params,
  });

  return response.items || [];
}
