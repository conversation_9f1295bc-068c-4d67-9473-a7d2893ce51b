import { requestClient } from '#/api/request';

/**
 * 电力运行报表数据类型定义
 */
export interface PowerOperationData {
  circuitName: string; // 回路名称
  date: number; // 采集时间（时间戳）
  ep: number; // Ep(kWh) - 电能
  uq: number; // Uq(V) - 电压Q相
  ub: number; // Ub(V) - 电压B相
  uc: number; // Uc(V) - 电压C相
  ia: number; // Ia(A) - 电流A相
  ib: number; // Ib(A) - 电流B相
  ic: number; // Ic(A) - 电流C相
  p: number; // P(kW) - 有功功率
  q: number; // Q(kVar) - 无功功率
  pf: number; // Pf - 功率因数
}

/**
 * 电力运行报表查询参数（业务参数）
 */
export interface PowerOperationQueryParams {
  startTime?: string; // 开始时间
  endTime?: string; // 结束时间
  reportType?: 'dayly' | 'monthly' | 'custom'; // 报表类型
  timeInterval?: string; // 时间间隔 (1:一分钟, 5:五分钟, 15:十五分钟, 30:半小时, 60:一小时)
  // 左侧筛选表单的参数
  enterpriseId?: string; // 企业ID
  energyType?: string; // 能源类型 (1:电, 2:气, 3:水等)
  siteId?: string; // 站点ID
  keyword?: string; // 关键字
  circuitNames?: string[]; // 选中的回路ID列表（复数形式）
}

/**
 * 分页参数
 */
export interface PaginationParams {
  page: number; // 页码
  pageSize: number; // 每页数量
}

/**
 * 完整的请求参数结构
 */
export interface PowerOperationRequestParams {
  query: PowerOperationQueryParams; // 查询参数
  pagination: PaginationParams; // 分页参数
}

/**
 * 电力运行报表响应数据
 */
export interface PowerOperationResponse {
  items: PowerOperationData[];
  total: number;
  page: number;
  pageSize: number;
  reportType?: string;
}

/**
 * 获取电力运行报表数据（POST方法）
 * @param requestParams 请求参数（包含query和pagination）
 * @returns 电力运行报表数据
 */
export async function getPowerOperationReportApi(
  requestParams: PowerOperationRequestParams,
): Promise<PowerOperationResponse> {
  return requestClient.post('/power/operation-report', requestParams);
}
