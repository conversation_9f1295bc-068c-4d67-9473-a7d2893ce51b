import { requestClient } from '#/api/request';

/**
 * 站点数据类型定义
 */
export interface SiteData {
  id: string; // 站点ID
  name: string; // 站点名称
  enterpriseId: string; // 所属企业ID
  address?: string; // 站点地址
  description?: string; // 站点描述
  status?: 'active' | 'inactive'; // 站点状态
  createdAt?: string; // 创建时间
  updatedAt?: string; // 更新时间
}

/**
 * 站点列表响应类型
 */
export interface SitesResponse {
  items: SiteData[];
  total: number;
  page: number;
  pageSize: number;
}

/**
 * 站点查询参数
 */
export interface SitesQueryParams {
  enterpriseId?: string; // 企业ID（必填）
  keyword?: string; // 搜索关键字
  status?: 'active' | 'inactive'; // 站点状态
}

/**
 * 站点请求参数结构
 */
export interface SitesRequestParams {
  query: SitesQueryParams;
  pagination: {
    page: number;
    pageSize: number;
  };
}

/**
 * 根据企业ID获取站点列表
 * @param requestParams 请求参数（包含query和pagination）
 * @returns 站点列表数据
 */
export async function getSitesByEnterpriseApi(
  requestParams: SitesRequestParams,
): Promise<SitesResponse> {
  return requestClient.post('/power/sites', requestParams);
}

/**
 * 获取所有站点列表（不分页）
 * @param enterpriseId 企业ID
 * @returns 站点列表数据
 */
export async function getAllSitesApi(enterpriseId?: string): Promise<SiteData[]> {
  const requestParams: SitesRequestParams = {
    query: {
      enterpriseId: enterpriseId || '',
    },
    pagination: {
      page: 1,
      pageSize: 1000, // 获取所有数据
    },
  };
  
  const response = await getSitesByEnterpriseApi(requestParams);
  return response.items;
}
