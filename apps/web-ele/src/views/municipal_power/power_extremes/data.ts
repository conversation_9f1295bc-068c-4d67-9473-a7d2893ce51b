import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';

import dayjs from 'dayjs';

// 报表类型定义
export type ReportType = 'custom' | 'dayly' | 'monthly';

// 时间格式化工具函数
function formatTime(timestamp: number, reportType: ReportType): string {
  if (!timestamp) return '';

  if (reportType === 'monthly') {
    return dayjs(timestamp).format('DD日 HH:mm');
  } else if (reportType === 'custom') {
    const formatted = dayjs(timestamp).format('YYYY-MM-DD HH:mm');
    return formatted; // 自定义报表显示完整日期时间
  }
  return dayjs(timestamp).format('HH:mm'); // 日报只显示时间
}

// 电力极值报表数据类型定义
export interface PowerExtremesData {
  circuitName: string; // 回路名称
  date: number; // 日期时间戳
  // 有功功率(kW)
  activePowerMaxValue: number; // 最大值数值
  activePowerMaxTime: number; // 最大值发生时间戳
  activePowerMinValue: number; // 最小值数值
  activePowerMinTime: number; // 最小值发生时间戳
  activePowerAvgValue: number; // 平均值
  // 无功功率(kVar)
  reactivePowerMaxValue: number; // 最大值数值
  reactivePowerMaxTime: number; // 最大值发生时间戳
  reactivePowerMinValue: number; // 最小值数值
  reactivePowerMinTime: number; // 最小值发生时间戳
  reactivePowerAvgValue: number; // 平均值
  // 视在功率(kVA)
  apparentPowerMaxValue: number; // 最大值数值
  apparentPowerMaxTime: number; // 最大值发生时间戳
  apparentPowerMinValue: number; // 最小值数值
  apparentPowerMinTime: number; // 最小值发生时间戳
  apparentPowerAvgValue: number; // 平均值
  // 功率因数
  powerFactorMaxValue: number; // 最大值数值
  powerFactorMaxTime: number; // 最大值发生时间戳
  powerFactorMinValue: number; // 最小值数值
  powerFactorMinTime: number; // 最小值发生时间戳
  powerFactorAvgValue: number; // 平均值
}

/**
 * 电力极值报表表格列配置函数
 *
 * 该函数用于生成电力极值报表表格的列配置，包含复杂的多级表头结构：
 * - 回路名称、日期（固定列）
 * - 有功功率、无功功率、视在功率、功率因数（主列）
 * - 每个功率类型下包含最大值、最小值、平均值（子列）
 * - 最大值和最小值下又包含数值和发生时间（孙列）
 *
 * @param reportType - 报表类型，'dayly' 为日报，'monthly' 为月报，'custom' 为自定义
 * @param onActionClick - 可选的操作按钮点击回调函数
 *
 * @returns VxeTableGridOptions<PowerExtremesData>['columns']
 *   返回 VXE Table 表格的列配置数组，包含三级表头结构
 */
export function useColumns(
  reportType: ReportType = 'dayly',
  onActionClick?: OnActionClickFn<PowerExtremesData>,
): VxeTableGridOptions<PowerExtremesData>['columns'] {
  console.log(`Creating columns for report type: ${reportType}`);

  const columns: VxeTableGridOptions<PowerExtremesData>['columns'] = [
    // 回路名称列 - 跨3行
    {
      field: 'circuitName',
      title: '回路名称',
      width: 120,
      fixed: 'left',
      align: 'center',
      headerAlign: 'center',
    },
  ];

  // 只有非自定义报表才显示日期列
  if (reportType !== 'custom') {
    console.log(`Adding date column for ${reportType} report`);
    columns.push({
      field: 'date',
      title: '日期',
      width: 120,
      fixed: 'left',
      align: 'center',
      headerAlign: 'center',
      formatter: ({ cellValue }) => {
        if (!cellValue) return '';
        // 根据报表类型格式化日期
        if (reportType === 'monthly') {
          return dayjs(cellValue).format('YYYY年MM月');
        }
        return dayjs(cellValue).format('YYYY-MM-DD');
      },
    });
  } else {
    console.log('Skipping date column for custom report');
  }

  // 添加功率相关列
  columns.push(
    // 有功功率(kW) - 主列
    {
      title: '有功功率(kW)',
      align: 'center',
      headerAlign: 'center',
      children: [
        // 最大值 - 子列
        {
          title: '最大值',
          align: 'center',
          headerAlign: 'center',
          children: [
            {
              field: 'activePowerMaxValue',
              title: '数值',
              width: 80,
              align: 'right',
              headerAlign: 'center',
              formatter: ({ cellValue }) => cellValue?.toFixed(2) || '0.00',
            },
            {
              field: 'activePowerMaxTime',
              title: '发生时间',
              width: reportType === 'custom' ? 140 : 120, // 自定义报表需要更宽的列
              align: 'center',
              headerAlign: 'center',
              formatter: ({ cellValue }) => formatTime(cellValue, reportType),
            },
          ],
        },
        // 最小值 - 子列
        {
          title: '最小值',
          align: 'center',
          headerAlign: 'center',
          children: [
            {
              field: 'activePowerMinValue',
              title: '数值',
              width: 80,
              align: 'right',
              headerAlign: 'center',
              formatter: ({ cellValue }) => cellValue?.toFixed(2) || '0.00',
            },
            {
              field: 'activePowerMinTime',
              title: '发生时间',
              width: reportType === 'custom' ? 140 : 120,
              align: 'center',
              headerAlign: 'center',
              formatter: ({ cellValue }) => formatTime(cellValue, reportType),
            },
          ],
        },
        // 平均值 - 子列（跨2行）
        {
          field: 'activePowerAvgValue',
          title: '平均值',
          width: 80,
          align: 'right',
          headerAlign: 'center',
          formatter: ({ cellValue }) => cellValue?.toFixed(2) || '0.00',
        },
      ],
    },
    // 无功功率(kVar) - 主列
    {
      title: '无功功率(kVar)',
      align: 'center',
      headerAlign: 'center',
      children: [
        // 最大值 - 子列
        {
          title: '最大值',
          align: 'center',
          headerAlign: 'center',
          children: [
            {
              field: 'reactivePowerMaxValue',
              title: '数值',
              width: 80,
              align: 'right',
              headerAlign: 'center',
              formatter: ({ cellValue }) => cellValue?.toFixed(2) || '0.00',
            },
            {
              field: 'reactivePowerMaxTime',
              title: '发生时间',
              width: reportType === 'custom' ? 140 : 120,
              align: 'center',
              headerAlign: 'center',
              formatter: ({ cellValue }) => formatTime(cellValue, reportType),
            },
          ],
        },
        // 最小值 - 子列
        {
          title: '最小值',
          align: 'center',
          headerAlign: 'center',
          children: [
            {
              field: 'reactivePowerMinValue',
              title: '数值',
              width: 80,
              align: 'right',
              headerAlign: 'center',
              formatter: ({ cellValue }) => cellValue?.toFixed(2) || '0.00',
            },
            {
              field: 'reactivePowerMinTime',
              title: '发生时间',
              width: reportType === 'custom' ? 140 : 120,
              align: 'center',
              headerAlign: 'center',
              formatter: ({ cellValue }) => formatTime(cellValue, reportType),
            },
          ],
        },
        // 平均值 - 子列（跨2行）
        {
          field: 'reactivePowerAvgValue',
          title: '平均值',
          width: 80,
          align: 'right',
          headerAlign: 'center',
          formatter: ({ cellValue }) => cellValue?.toFixed(2) || '0.00',
        },
      ],
    },
    // 视在功率(kVA) - 主列
    {
      title: '视在功率(kVA)',
      align: 'center',
      headerAlign: 'center',
      children: [
        // 最大值 - 子列
        {
          title: '最大值',
          align: 'center',
          headerAlign: 'center',
          children: [
            {
              field: 'apparentPowerMaxValue',
              title: '数值',
              width: 80,
              align: 'right',
              headerAlign: 'center',
              formatter: ({ cellValue }) => cellValue?.toFixed(2) || '0.00',
            },
            {
              field: 'apparentPowerMaxTime',
              title: '发生时间',
              width: reportType === 'custom' ? 140 : 120,
              align: 'center',
              headerAlign: 'center',
              formatter: ({ cellValue }) => formatTime(cellValue, reportType),
            },
          ],
        },
        // 最小值 - 子列
        {
          title: '最小值',
          align: 'center',
          headerAlign: 'center',
          children: [
            {
              field: 'apparentPowerMinValue',
              title: '数值',
              width: 80,
              align: 'right',
              headerAlign: 'center',
              formatter: ({ cellValue }) => cellValue?.toFixed(2) || '0.00',
            },
            {
              field: 'apparentPowerMinTime',
              title: '发生时间',
              width: reportType === 'custom' ? 140 : 120,
              align: 'center',
              headerAlign: 'center',
              formatter: ({ cellValue }) => formatTime(cellValue, reportType),
            },
          ],
        },
        // 平均值 - 子列（跨2行）
        {
          field: 'apparentPowerAvgValue',
          title: '平均值',
          width: 80,
          align: 'right',
          headerAlign: 'center',
          formatter: ({ cellValue }) => cellValue?.toFixed(2) || '0.00',
        },
      ],
    },
    // 功率因数 - 主列
    {
      title: '功率因数',
      align: 'center',
      headerAlign: 'center',
      children: [
        // 最大值 - 子列
        {
          title: '最大值',
          align: 'center',
          headerAlign: 'center',
          children: [
            {
              field: 'powerFactorMaxValue',
              title: '数值',
              width: 80,
              align: 'right',
              headerAlign: 'center',
              formatter: ({ cellValue }) => cellValue?.toFixed(3) || '0.000',
            },
            {
              field: 'powerFactorMaxTime',
              title: '发生时间',
              width: reportType === 'custom' ? 140 : 120,
              align: 'center',
              headerAlign: 'center',
              formatter: ({ cellValue }) => formatTime(cellValue, reportType),
            },
          ],
        },
        // 最小值 - 子列
        {
          title: '最小值',
          align: 'center',
          headerAlign: 'center',
          children: [
            {
              field: 'powerFactorMinValue',
              title: '数值',
              width: 80,
              align: 'right',
              headerAlign: 'center',
              formatter: ({ cellValue }) => cellValue?.toFixed(3) || '0.000',
            },
            {
              field: 'powerFactorMinTime',
              title: '发生时间',
              width: reportType === 'custom' ? 140 : 120,
              align: 'center',
              headerAlign: 'center',
              formatter: ({ cellValue }) => formatTime(cellValue, reportType),
            },
          ],
        },
        // 平均值 - 子列（跨2行）
        {
          field: 'powerFactorAvgValue',
          title: '平均值',
          width: 80,
          align: 'right',
          headerAlign: 'center',
          formatter: ({ cellValue }) => cellValue?.toFixed(3) || '0.000',
        },
      ],
    },
  );

  return columns;
}
