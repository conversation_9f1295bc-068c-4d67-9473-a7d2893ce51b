import type { VbenFormProps } from '@vben-core/form-ui';

import type { ReportType } from './data';

import dayjs from 'dayjs';

/**
 * 电力极值报表表单配置
 * 根据不同的报表类型返回对应的表单schema配置
 */

/**
 * 日报表单配置
 * 使用单个日期选择器
 */
export function getDaylyFormSchema(): VbenFormProps['schema'] {
  return [
    {
      component: 'DatePicker',
      defaultValue: dayjs().format('YYYY-MM-DD'),
      fieldName: 'date',
      label: '日期',
      componentProps: (values, formApi) => ({
        placeholder: '请选择查询日期',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
        clearable: false,
        size: 'default',
        type: 'date',
        class: 'max-w-xs', // 日期选择器使用较小宽度
      }),
    },
    {
      component: 'Select',
      defaultValue: '1', // 默认选中第一项：功率
      componentProps: (values, formApi) => ({
        allowClear: false,
        options: [
          {
            label: '功率',
            value: '1',
          },
          {
            label: '电流',
            value: '2',
          },
          {
            label: '相电压',
            value: '3',
          },
          {
            label: '线电压',
            value: '4',
          },
          {
            label: '不平衡度',
            value: '5',
          },
          {
            label: '电压谐波',
            value: '6',
          },
          {
            label: '电流谐波',
            value: '7',
          },
        ],
        placeholder: '请选择',
        class: 'max-w-sm', // 下拉选择器使用中等宽度
      }),
      fieldName: 'electricalCategory',
      label: '电力类别',
    },
  ];
}

/**
 * 月报表单配置
 * 使用月份选择器
 */
export function getMonthlyFormSchema(): VbenFormProps['schema'] {
  return [
    {
      component: 'DatePicker',
      defaultValue: dayjs().format('YYYY-MM'),
      fieldName: 'month',
      label: '月份',
      componentProps: (values, formApi) => ({
        placeholder: '请选择查询月份',
        format: 'YYYY-MM',
        valueFormat: 'YYYY-MM',
        clearable: false,
        size: 'default',
        type: 'month',
        class: 'max-w-xs', // 月份选择器使用较小宽度
      }),
    },
    {
      component: 'Select',
      defaultValue: '1', // 默认选中第一项：功率
      componentProps: (values, formApi) => ({
        allowClear: false,
        options: [
          {
            label: '功率',
            value: '1',
          },
          {
            label: '电流',
            value: '2',
          },
          {
            label: '相电压',
            value: '3',
          },
          {
            label: '线电压',
            value: '4',
          },
          {
            label: '不平衡度',
            value: '5',
          },
          {
            label: '电压谐波',
            value: '6',
          },
          {
            label: '电流谐波',
            value: '7',
          },
        ],
        placeholder: '请选择',
        class: 'max-w-sm', // 下拉选择器使用中等宽度
      }),
      fieldName: 'electricalCategory',
      label: '电力类别',
    },
  ];
}

/**
 * 自定义报表表单配置
 * 使用日期范围选择器
 */
export function getCustomFormSchema(): VbenFormProps['schema'] {
  return [
    {
      component: 'RangePicker',
      defaultValue: [
        dayjs().subtract(7, 'days').format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ],
      fieldName: 'dateRange',
      label: '日期',
      componentProps: (values, formApi) => ({
        startPlaceholder: '开始日期',
        endPlaceholder: '结束日期',
        rangeSeparator: '至',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
        clearable: false,
        size: 'default',
        type: 'daterange',
        class: 'max-w-md', // 日期范围选择器使用较大宽度
      }),
    },
    {
      component: 'Select',
      defaultValue: '1', // 默认选中第一项：功率
      componentProps: (values, formApi) => ({
        allowClear: false,
        options: [
          {
            label: '功率',
            value: '1',
          },
          {
            label: '电流',
            value: '2',
          },
          {
            label: '相电压',
            value: '3',
          },
          {
            label: '线电压',
            value: '4',
          },
          {
            label: '不平衡度',
            value: '5',
          },
          {
            label: '电压谐波',
            value: '6',
          },
          {
            label: '电流谐波',
            value: '7',
          },
        ],
        placeholder: '请选择',
        class: 'max-w-sm', // 下拉选择器使用中等宽度
      }),
      fieldName: 'electricalCategory',
      label: '电力类别',
    },
  ];
}

/**
 * 根据报表类型获取对应的表单schema配置
 * @param reportType 报表类型
 * @returns 表单schema配置数组
 */
export function getFormSchemaByType(
  reportType: ReportType,
): VbenFormProps['schema'] {
  switch (reportType) {
    case 'custom': {
      return getCustomFormSchema();
    }
    case 'dayly': {
      return getDaylyFormSchema();
    }
    case 'monthly': {
      return getMonthlyFormSchema();
    }
    default: {
      return getDaylyFormSchema();
    }
  }
}

/**
 * 创建完整的表单配置
 * @param reportType 报表类型
 * @returns 完整的表单配置对象
 */
export function createFormOptions(reportType: ReportType): VbenFormProps {
  return {
    // 默认展开搜索表单
    collapsed: false,
    // 所有表单项共用配置
    commonConfig: {
      // 所有表单项统一样式
      componentProps: {
        // class: 'w-full', // 所有表单项的控件样式
      },
      // 控制表单控件的最大宽度，避免在网格布局中过大
      controlClass: 'max-w-xs', // 限制控件最大宽度为 max-w-xs (20rem/320px)
      // labelClass: 'justify-start',
      labelWidth: 80,
      colon: true,
      // formItemClass: 'flex-nowrap',
    },
    // 根据报表类型获取对应的schema配置
    schema: getFormSchemaByType(reportType),
    // 控制表单是否显示折叠按钮
    showCollapseButton: false,
    // 是否在字段值改变时提交表单
    submitOnChange: true,
    // 按下回车时是否提交表单
    submitOnEnter: false,
    // 表单布局
    layout: 'horizontal',
    // 使用自定义类名，通过 CSS 覆盖实现 flexbox 布局
    wrapperClass: 'compact-form-layout',
  };
}
