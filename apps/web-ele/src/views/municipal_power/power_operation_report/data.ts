import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';

// 报表类型定义
export type ReportType = 'custom' | 'dayly' | 'monthly';

// 电力运行报表数据类型定义
export interface PowerOperationData {
  circuitName: string; // 回路名称
  date: number; // 采集时间（时间戳）
  ep: number; // Ep(kWh) - 电能
  uq: number; // Uq(V) - 电压Q相
  ub: number; // Ub(V) - 电压B相
  uc: number; // Uc(V) - 电压C相
  ia: number; // Ia(A) - 电流A相
  ib: number; // Ib(A) - 电流B相
  ic: number; // Ic(A) - 电流C相
  p: number; // P(kW) - 有功功率
  q: number; // Q(kVar) - 无功功率
  pf: number; // Pf - 功率因数
}

/**
 * 电力运行报表表格列配置函数
 *
 * 该函数用于生成电力运行报表表格的列配置，包含以下列：
 * - 回路名称、采集时间（固定列）
 * - Ep(kWh)、Uq(V)、Ub(V)、Uc(V)、Ia(A)、Ib(A)、Ic(A)、P(kW)、Q(kVar)、Pf
 *
 * @param reportType - 报表类型，'dayly' 为日报，'monthly' 为月报，'custom' 为自定义
 * @param onActionClick - 可选的操作按钮点击回调函数
 *
 * @returns VxeTableGridOptions<PowerOperationData>['columns']
 *   返回 VXE Table 表格的列配置数组
 */
export function useColumns(
  reportType: ReportType = 'dayly',
  onActionClick?: OnActionClickFn<PowerOperationData>,
): VxeTableGridOptions<PowerOperationData>['columns'] {
  console.log(`Creating columns for report type: ${reportType}`);

  const columns: VxeTableGridOptions<PowerOperationData>['columns'] = [
    // 回路名称列
    {
      field: 'circuitName',
      title: '回路名称',
      minWidth: 120,
      fixed: 'left',
      align: 'center',
      headerAlign: 'center',
    },
    // 采集时间列
    {
      field: 'date',
      title: '采集时间',
      minWidth: 160,
      fixed: 'left',
      align: 'center',
      headerAlign: 'center',
      sortable: true, // 启用排序功能
      formatter: ({ cellValue }) => {
        if (!cellValue) return '';
        // 将时间戳格式化为可读的日期时间
        return new Date(cellValue).toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
        });
      },
    },
  ];

  // 添加其他数据列
  columns.push(
    // Ep(kWh) - 电能
    {
      field: 'ep',
      title: 'Ep(kWh)',
      minWidth: 100,
      align: 'right',
      headerAlign: 'center',
      formatter: ({ cellValue }) => cellValue?.toFixed(1) || '0.0',
    },
    // Uq(V) - 电压Q相
    {
      field: 'uq',
      title: 'Uq(V)',
      minWidth: 80,
      align: 'right',
      headerAlign: 'center',
      formatter: ({ cellValue }) => cellValue?.toString() || '0',
    },
    // Ub(V) - 电压B相
    {
      field: 'ub',
      title: 'Ub(V)',
      minWidth: 80,
      align: 'right',
      headerAlign: 'center',
      formatter: ({ cellValue }) => cellValue?.toString() || '0',
    },
    // Uc(V) - 电压C相
    {
      field: 'uc',
      title: 'Uc(V)',
      minWidth: 80,
      align: 'right',
      headerAlign: 'center',
      formatter: ({ cellValue }) => cellValue?.toString() || '0',
    },
    // Ia(A) - 电流A相
    {
      field: 'ia',
      title: 'Ia(A)',
      minWidth: 80,
      align: 'right',
      headerAlign: 'center',
      formatter: ({ cellValue }) => cellValue?.toFixed(1) || '0.0',
    },
    // Ib(A) - 电流B相
    {
      field: 'ib',
      title: 'Ib(A)',
      minWidth: 80,
      align: 'right',
      headerAlign: 'center',
      formatter: ({ cellValue }) => cellValue?.toFixed(1) || '0.0',
    },
    // Ic(A) - 电流C相
    {
      field: 'ic',
      title: 'Ic(A)',
      minWidth: 80,
      align: 'right',
      headerAlign: 'center',
      formatter: ({ cellValue }) => cellValue?.toFixed(1) || '0.0',
    },
    // P(kW) - 有功功率
    {
      field: 'p',
      title: 'P(kW)',
      minWidth: 80,
      align: 'right',
      headerAlign: 'center',
      formatter: ({ cellValue }) => cellValue?.toFixed(1) || '0.0',
    },
    // Q(kVar) - 无功功率
    {
      field: 'q',
      title: 'Q(kVar)',
      minWidth: 80,
      align: 'right',
      headerAlign: 'center',
      formatter: ({ cellValue }) => cellValue?.toFixed(1) || '0.0',
    },
    // Pf - 功率因数
    {
      field: 'pf',
      title: 'Pf',
      minWidth: 80,
      align: 'right',
      headerAlign: 'center',
      formatter: ({ cellValue }) => cellValue?.toFixed(2) || '0.00',
    },
  );

  return columns;
}
