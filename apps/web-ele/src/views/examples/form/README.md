# Form Examples

## 页面列表

### 基础表单示例
- **basic.vue** - 基础表单功能演示
- **query.vue** - 查询表单演示
- **rules.vue** - 表单验证规则演示
- **dynamic.vue** - 动态表单演示
- **custom-layout.vue** - 自定义布局演示
- **custom.vue** - 自定义组件演示
- **api.vue** - API 表单演示
- **merge.vue** - 表单合并演示

### 表单紧凑布局演示
- **compact-layout.vue** - 表单紧凑布局方案演示

## 表单紧凑布局演示

### 访问路径
`/examples/form/compact-layout`

### 功能特点
1. **三种布局方案对比**：
   - 默认网格布局
   - 自适应网格布局
   - CSS 强制覆盖 Flexbox 布局（推荐）

2. **解决的问题**：
   - Vben Form 硬编码 `grid` 类的问题
   - 表单项紧密排列的实现
   - 表单控件宽度控制

3. **主题适配**：
   - 支持深色/浅色模式
   - 使用 CSS 变量确保颜色正确显示
   - 响应式设计

### 技术要点

#### 核心解决方案
```scss
.compact-form-layout {
  display: flex !important;
  flex-wrap: wrap !important;
  align-items: center !important;
  gap: 1rem 2.5rem !important;
  justify-content: flex-start !important;
  
  // 确保表单控件保持合适的宽度
  .el-select,
  .el-date-editor,
  .el-input {
    min-width: 180px !important;
  }
}
```

#### 颜色系统
使用 CSS 变量而不是 Tailwind 类名：
```css
/* 正确的方式 */
color: hsl(var(--foreground))
background-color: hsl(var(--primary) / 0.1)
```

### 相关文档
- [表单紧凑布局解决方案](../municipal_power/power_extremes/compact-form-solutions.md)
- [表单控件尺寸控制指南](../municipal_power/power_extremes/form-control-size-guide.md)
