<script lang="ts" setup>
import { ref } from 'vue';
import { useVbenForm } from '#/adapter/form';
import { ElMessage, ElCard, ElDivider } from 'element-plus';
import dayjs from 'dayjs';

/**
 * 表单紧凑布局演示
 *
 * 演示如何解决 Vben Form 表单项紧密排列的问题
 * 包含三种不同的布局方案对比
 */

// 方案一：默认网格布局（原始方案）
const [DefaultForm] = useVbenForm({
  commonConfig: {
    controlClass: 'max-w-xs',
    labelWidth: 80,
    colon: true,
  },
  schema: [
    {
      component: 'DatePicker',
      fieldName: 'date1',
      label: '日期',
      defaultValue: dayjs().format('YYYY-MM-DD'),
      componentProps: (values, formApi) => ({
        placeholder: '请选择日期',
        type: 'date',
        class: 'max-w-xs',
      }),
    },
    {
      component: 'Select',
      fieldName: 'type1',
      label: '类型',
      defaultValue: '1',
      componentProps: (values, formApi) => ({
        options: [
          { label: '功率', value: '1' },
          { label: '电流', value: '2' },
          { label: '电压', value: '3' },
        ],
        class: 'max-w-sm',
      }),
    },
  ],
  layout: 'horizontal',
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
  handleSubmit: (values) => {
    ElMessage.success(`默认布局提交：${JSON.stringify(values)}`);
  },
});

// 方案二：自适应网格布局
const [AutoFitForm] = useVbenForm({
  commonConfig: {
    controlClass: 'max-w-xs',
    labelWidth: 80,
    colon: true,
  },
  schema: [
    {
      component: 'DatePicker',
      fieldName: 'date2',
      label: '日期',
      defaultValue: dayjs().format('YYYY-MM-DD'),
      componentProps: (values, formApi) => ({
        placeholder: '请选择日期',
        type: 'date',
        class: 'max-w-xs',
      }),
    },
    {
      component: 'Select',
      fieldName: 'type2',
      label: '类型',
      defaultValue: '1',
      componentProps: (values, formApi) => ({
        options: [
          { label: '功率', value: '1' },
          { label: '电流', value: '2' },
          { label: '电压', value: '3' },
        ],
        class: 'max-w-sm',
      }),
    },
  ],
  layout: 'horizontal',
  wrapperClass: 'grid grid-cols-[repeat(auto-fit,minmax(min-content,max-content))] gap-x-6 gap-y-4 items-center',
  handleSubmit: (values) => {
    ElMessage.success(`自适应网格提交：${JSON.stringify(values)}`);
  },
});

// 方案三：CSS 强制覆盖 Flexbox 布局（推荐）
const [FlexForm] = useVbenForm({
  commonConfig: {
    controlClass: 'max-w-xs',
    labelWidth: 80,
    colon: true,
  },
  schema: [
    {
      component: 'DatePicker',
      fieldName: 'date3',
      label: '日期',
      defaultValue: dayjs().format('YYYY-MM-DD'),
      componentProps: (values, formApi) => ({
        placeholder: '请选择日期',
        type: 'date',
        class: 'max-w-xs',
      }),
    },
    {
      component: 'Select',
      fieldName: 'type3',
      label: '类型',
      defaultValue: '1',
      componentProps: (values, formApi) => ({
        options: [
          { label: '功率', value: '1' },
          { label: '电流', value: '2' },
          { label: '电压', value: '3' },
        ],
        class: 'max-w-sm',
      }),
    },
    {
      component: 'RangePicker',
      fieldName: 'range3',
      label: '范围',
      defaultValue: [
        dayjs().subtract(7, 'days').format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ],
      componentProps: (values, formApi) => ({
        type: 'daterange',
        startPlaceholder: '开始',
        endPlaceholder: '结束',
        class: 'max-w-md',
      }),
    },
  ],
  layout: 'horizontal',
  wrapperClass: 'compact-form-layout', // 使用自定义类名
  handleSubmit: (values) => {
    ElMessage.success(`CSS强制覆盖布局提交：${JSON.stringify(values)}`);
  },
});
</script>

<template>
  <div class="p-6 space-y-8">
    <h1 class="text-2xl font-bold mb-6" style="color: hsl(var(--foreground))">表单布局方案演示</h1>

    <!-- 方案一：默认网格布局 -->
    <ElCard>
      <template #header>
        <div class="flex justify-between items-center">
          <h3 class="text-lg font-semibold" style="color: hsl(var(--foreground))">方案一：默认网格布局</h3>
          <span class="text-sm" style="color: hsl(var(--foreground) / 0.7)">wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'</span>
        </div>
      </template>
      <div class="mb-4 p-3 rounded text-sm" style="background-color: hsl(var(--warning) / 0.1); border: 1px solid hsl(var(--warning) / 0.2);">
        <strong style="color: hsl(var(--foreground))">特点：</strong>
        <span style="color: hsl(var(--foreground) / 0.8)">每个表单项占据一个完整的网格列，在大屏幕上可能显得过于分散。</span>
      </div>
      <DefaultForm />
    </ElCard>

    <!-- 方案二：自适应网格布局 -->
    <ElCard>
      <template #header>
        <div class="flex justify-between items-center">
          <h3 class="text-lg font-semibold" style="color: hsl(var(--foreground))">方案二：自适应网格布局</h3>
          <span class="text-sm" style="color: hsl(var(--foreground) / 0.7)">grid-cols-[repeat(auto-fit,minmax(min-content,max-content))]</span>
        </div>
      </template>
      <div class="mb-4 p-3 rounded text-sm" style="background-color: hsl(var(--primary) / 0.1); border: 1px solid hsl(var(--primary) / 0.2);">
        <strong style="color: hsl(var(--foreground))">特点：</strong>
        <span style="color: hsl(var(--foreground) / 0.8)">表单项根据内容宽度自动排列，实现紧密布局，无需修改源码。</span>
      </div>
      <AutoFitForm />
    </ElCard>

    <!-- 方案三：CSS 强制覆盖 Flexbox 布局 -->
    <ElCard>
      <template #header>
        <div class="flex justify-between items-center">
          <h3 class="text-lg font-semibold" style="color: hsl(var(--foreground))">方案三：CSS 强制覆盖 Flexbox 布局（推荐）</h3>
          <span class="text-sm" style="color: hsl(var(--foreground) / 0.7)">使用 !important 强制覆盖硬编码的 grid 类</span>
        </div>
      </template>
      <div class="mb-4 p-3 rounded text-sm" style="background-color: hsl(var(--primary) / 0.15); border: 1px solid hsl(var(--primary) / 0.3);">
        <strong style="color: hsl(var(--foreground))">特点：</strong>
        <span style="color: hsl(var(--foreground) / 0.8)">完全解决 Vben Form 硬编码 grid 问题，实现真正的紧密排列效果。</span>
      </div>
      <div class="flex-form-demo">
        <FlexForm />
      </div>
    </ElCard>

    <!-- 推荐方案说明 -->
    <ElCard>
      <template #header>
        <h3 class="text-lg font-semibold" style="color: hsl(var(--primary))">🎯 推荐方案</h3>
      </template>
      <div class="space-y-4">
        <div class="p-4 rounded" style="background-color: hsl(var(--primary) / 0.1); border: 1px solid hsl(var(--primary) / 0.2);">
          <h4 class="font-semibold mb-2" style="color: hsl(var(--foreground))">方案三：CSS 强制覆盖（推荐）</h4>
          <ul class="list-disc list-inside space-y-1 text-sm" style="color: hsl(var(--foreground) / 0.8)">
            <li>✅ 完全解决硬编码 grid 问题</li>
            <li>✅ 最大的布局控制灵活性</li>
            <li>✅ 可以精确控制间距和对齐</li>
            <li>✅ 支持复杂的响应式布局</li>
            <li>⚠️ 需要使用 !important（但这是必要的）</li>
          </ul>
        </div>

        <div class="p-4 rounded" style="background-color: hsl(var(--warning) / 0.1); border: 1px solid hsl(var(--warning) / 0.2);">
          <h4 class="font-semibold mb-2" style="color: hsl(var(--foreground))">⚠️ 重要发现</h4>
          <p class="text-sm mb-2" style="color: hsl(var(--foreground) / 0.8)">
            <strong style="color: hsl(var(--foreground))">Vben Form 硬编码问题：</strong>在源码 <code class="px-1 rounded" style="background-color: hsl(var(--muted))">form-render/form.vue</code> 第147行硬编码了 <code class="px-1 rounded" style="background-color: hsl(var(--muted))">class="grid"</code>，
            这导致任何在 <code class="px-1 rounded" style="background-color: hsl(var(--muted))">wrapperClass</code> 中设置的 <code class="px-1 rounded" style="background-color: hsl(var(--muted))">flex</code> 类都会被覆盖。
          </p>
          <p class="text-sm" style="color: hsl(var(--foreground) / 0.8)">
            <strong style="color: hsl(var(--foreground))">解决方案：</strong>必须使用 CSS <code class="px-1 rounded" style="background-color: hsl(var(--muted))">!important</code> 来强制覆盖硬编码的样式。
          </p>
        </div>
      </div>
    </ElCard>
  </div>
</template>

<style scoped lang="scss">
// 方案三：CSS 强制覆盖 Flexbox 布局的实现
.flex-form-demo {
  :deep(.compact-form-layout) {
    // 使用 !important 强制覆盖硬编码的 grid 类
    display: flex !important;
    flex-wrap: wrap !important;
    align-items: center !important;
    gap: 1rem 2.5rem !important;
    justify-content: flex-start !important;

    @media (min-width: 1200px) {
      flex-wrap: nowrap !important;
      gap: 1rem 3rem !important;
    }

    @media (max-width: 767px) {
      flex-direction: column !important;
      align-items: stretch !important;
      gap: 1rem !important;
    }
  }

  :deep(.compact-form-layout > *) {
    flex: 0 0 auto !important;
    width: auto !important;
    margin-bottom: 0 !important;

    @media (max-width: 767px) {
      flex: 1 1 100% !important;
      width: 100% !important;
    }
  }

  // 操作按钮区域靠右对齐 - 覆盖内部组件的 w-full
  :deep(.compact-form-layout > *:last-child) {
    margin-left: auto !important;
    flex: 0 0 auto !important;
    width: auto !important; // 覆盖 w-full

    // 覆盖内部组件的 w-full 类
    .w-full {
      width: auto !important;
    }

    @media (max-width: 767px) {
      margin-left: 0 !important;
      flex: 1 1 100% !important;
      width: 100% !important;

      .w-full {
        width: 100% !important;
      }
    }
  }

  :deep(.compact-form-layout .el-select),
  :deep(.compact-form-layout .el-date-editor),
  :deep(.compact-form-layout .el-input) {
    min-width: 180px !important;
  }
}
</style>

<!-- 全局样式，确保 compact-form-layout 生效 -->
<style lang="scss">
.compact-form-layout {
  display: flex !important;
  flex-wrap: wrap !important;
  align-items: center !important;
  gap: 1rem 2.5rem !important;
  justify-content: flex-start !important;

  @media (min-width: 1200px) {
    flex-wrap: nowrap !important;
    gap: 1rem 3rem !important;
  }

  @media (max-width: 767px) {
    flex-direction: column !important;
    align-items: stretch !important;
    gap: 1rem !important;
  }

  // 表单项容器样式
  > * {
    flex: 0 0 auto !important;
    margin-bottom: 0 !important;

    @media (max-width: 767px) {
      flex: 1 1 100% !important;
      width: 100% !important;
    }
  }

  // 操作按钮区域特殊处理 - 紧贴表单项，空间不够时自动换行
  > *:last-child {
    flex: 0 1 auto !important; // 允许收缩，空间不够时换行
    width: auto !important; // 覆盖 w-full 的 width: 100%
    min-width: fit-content !important; // 最小宽度为内容宽度

    // 强制覆盖组件内联样式 - 使用更高优先级
    grid-column: unset !important; // 覆盖 col-span-full 和内联样式
    margin-left: 0 !important; // 覆盖内联样式的 margin-left: auto

    // 针对内部的 form-actions 组件
    &.w-full {
      width: auto !important; // 覆盖组件自身的 w-full
    }

    &.col-span-full {
      grid-column: unset !important; // 覆盖 col-span-full 类
    }

    &.text-right {
      text-align: left !important; // 覆盖右对齐
    }

    .w-full {
      width: auto !important; // 覆盖组件内部的 w-full
    }

    @media (max-width: 767px) {
      flex: 1 1 100% !important; // 小屏幕下独占一行
      width: 100% !important; // 小屏幕下恢复全宽
      grid-column: 1 / -1 !important; // 小屏幕下恢复全列
      margin-left: auto !important; // 小屏幕下恢复右对齐

      &.w-full {
        width: 100% !important;
      }

      &.text-right {
        text-align: right !important; // 小屏幕下恢复右对齐
      }

      .w-full {
        width: 100% !important;
      }
    }
  }

  // 确保表单控件保持合适的宽度
  .el-select,
  .el-date-editor,
  .el-input,
  .el-cascader {
    min-width: 180px !important;
    width: auto !important;
  }

  // 日期范围选择器需要更大的宽度
  .el-date-editor--daterange,
  .el-date-editor--datetimerange {
    min-width: 280px !important;
  }

  // 下拉选择器特殊处理
  .el-select {
    .el-input {
      min-width: 180px !important;
    }
  }
}

div.compact-form-layout.grid {
  display: flex !important;
  flex-wrap: wrap !important;
  align-items: center !important;
  gap: 1rem 2.5rem !important;
  justify-content: flex-start !important;
}
</style>
