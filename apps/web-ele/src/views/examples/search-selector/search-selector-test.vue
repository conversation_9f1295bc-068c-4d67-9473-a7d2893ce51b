<script setup lang="ts">
import { ref } from 'vue';

import { Page } from '@vben/common-ui';

import { useVbenForm } from '#/adapter/form';
import { searchEnterprisesApi } from '#/api/energy/enterprise';

// 表单数据
const formData = ref({
  enterprise: undefined,
});

// 创建表单
const [TestForm] = useVbenForm({
  // 默认展开
  collapsed: false,
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  // 提交函数
  handleSubmit: (values) => {
    console.log('Form submitted:', values);
  },
  // 水平布局，label和input在同一行
  layout: 'horizontal',
  schema: [
    {
      component: 'SearchSelector',
      fieldName: 'enterprise',
      label: '企业选择',
      componentProps: {
        class: 'w-full sm:max-w-xs md:max-w-sm lg:max-w-md xl:max-w-sm',
        placeholder: '请选择企业',
        searchPlaceholder: '搜索企业名称',
        buttonType: 'default',
        buttonSize: 'default',
        placement: 'bottom-start',
        popoverWidth: 300,
        filterable: true,
        remote: false, // 设置为 false，这样组件会在挂载时自动加载数据
        // 使用异步数据源获取企业列表
        dataSource: async (keyword?: string) => {
          try {
            console.log(
              'SearchSelector dataSource called with keyword:',
              keyword,
            );
            // 调用企业搜索API，当 keyword 为空或 undefined 时，获取所有企业列表
            const result = await searchEnterprisesApi(keyword || undefined);
            console.log('Enterprise list loaded:', result.length, 'items');
            return result;
          } catch (error) {
            console.error('获取企业列表失败:', error);
            // 如果API调用失败，返回模拟数据作为降级方案（使用统一的企业ID格式）
            const mockData = [
              { label: '华能电力集团', value: '91110000MA001234XY' },
              { label: '国家电网公司', value: '91110000MA005678AB' },
              { label: '南方电网公司', value: '91110000MA009012CD' },
              { label: '中国电建集团', value: '91110000MA003456EF' },
              { label: '大唐发电集团', value: '91110000MA007890GH' },
            ];

            // 如果有搜索关键词，进行过滤
            if (keyword && keyword.trim()) {
              return mockData.filter((item) =>
                item.label.toLowerCase().includes(keyword.toLowerCase()),
              );
            }

            console.log(
              'Using fallback enterprise data:',
              mockData.length,
              'items',
            );
            return mockData;
          }
        },
        // 监听企业选择变化
        onChange: (value: number | string, option: any) => {
          console.log('Enterprise selected:', value, option);
          formData.value.enterprise = value;
        },
      },
    },
    {
      component: 'Input',
      fieldName: 'testInput',
      label: '测试输入',
      componentProps: {
        placeholder: '请输入测试内容',
      },
    },
  ],
  // 大屏一行显示2个，中屏一行显示2个，小屏一行显示1个
  wrapperClass: 'grid-cols-1 md:grid-cols-2',
});
</script>

<template>
  <Page auto-content-height>
    <div class="p-4 space-y-6">
      <div>
        <h2 class="mb-2 text-2xl font-bold">SearchSelector 组件测试</h2>
        <p class="text-muted-foreground">
          现在使用 ElRadio 组件实现选项列表，提供更好的视觉反馈和用户体验
        </p>
      </div>

      <!-- 功能说明 -->
      <div class="rounded-lg border border-border bg-card p-4">
        <h3 class="mb-3 text-lg font-semibold text-foreground">✨ 新功能特性</h3>
        <ul class="space-y-2 text-sm text-muted-foreground">
          <li class="flex items-center gap-2">
            <span class="h-1.5 w-1.5 rounded-full bg-primary"></span>
            使用 ElRadio 组件替代原有的选项列表样式
          </li>
          <li class="flex items-center gap-2">
            <span class="h-1.5 w-1.5 rounded-full bg-primary"></span>
            提供清晰的选中状态视觉反馈
          </li>
          <li class="flex items-center gap-2">
            <span class="h-1.5 w-1.5 rounded-full bg-primary"></span>
            支持深色模式和主题适配
          </li>
          <li class="flex items-center gap-2">
            <span class="h-1.5 w-1.5 rounded-full bg-primary"></span>
            保持原有的搜索、键盘导航等功能
          </li>
        </ul>
      </div>

      <!-- 使用说明 -->
      <div class="rounded-lg border border-border bg-card p-4">
        <h3 class="mb-3 text-lg font-semibold text-foreground">📖 使用说明</h3>
        <ol class="space-y-2 text-sm text-muted-foreground">
          <li class="flex gap-2">
            <span class="flex h-5 w-5 items-center justify-center rounded-full bg-primary/10 text-xs font-medium text-primary">1</span>
            点击下方的"请选择企业"按钮打开选择器
          </li>
          <li class="flex gap-2">
            <span class="flex h-5 w-5 items-center justify-center rounded-full bg-primary/10 text-xs font-medium text-primary">2</span>
            在搜索框中输入关键词进行筛选
          </li>
          <li class="flex gap-2">
            <span class="flex h-5 w-5 items-center justify-center rounded-full bg-primary/10 text-xs font-medium text-primary">3</span>
            点击任意选项或其 Radio 按钮进行选择
          </li>
          <li class="flex gap-2">
            <span class="flex h-5 w-5 items-center justify-center rounded-full bg-primary/10 text-xs font-medium text-primary">4</span>
            观察选中状态的 Radio 按钮样式变化
          </li>
        </ol>
      </div>

      <div class="mb-6">
        <h3 class="mb-2 text-lg font-semibold">当前表单数据：</h3>
        <pre class="rounded bg-muted p-3 text-sm text-muted-foreground">{{
          JSON.stringify(formData, null, 2)
        }}</pre>
      </div>

      <div class="rounded-lg border border-border bg-card p-4">
        <h3 class="mb-4 text-lg font-semibold">表单测试</h3>
        <TestForm />
      </div>
    </div>
  </Page>
</template>

<style scoped>
.page-container {
  @apply h-full;
}
</style>
