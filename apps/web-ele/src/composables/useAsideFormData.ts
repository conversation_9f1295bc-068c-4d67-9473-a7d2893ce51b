import type { SiteData } from '#/api/power/sites';

import { computed, readonly, ref } from 'vue';

import { searchEnterprisesApi } from '#/api/energy/enterprise';
import { getAllSitesApi } from '#/api/power/sites';
import { getCircuitTreeApi } from '#/api/power/circuits';

/**
 * 企业数据类型
 */
export interface Enterprise {
  label: string;
  value: string;
  [key: string]: any;
}

/**
 * 树形节点数据类型
 */
export interface TreeNode {
  id: string;
  label: string;
  type: 'group' | 'circuit';
  children?: TreeNode[];
  [key: string]: any;
}

/**
 * 左侧筛选表单数据管理
 */
export function useAsideFormData() {
  // 响应式数据
  const enterprises = ref<Enterprise[]>([]);
  const sites = ref<SiteData[]>([]);
  const selectedEnterpriseId = ref<string>('');
  const selectedSiteId = ref<string>('');
  const loading = ref(false);
  const error = ref<string>('');
  const initialized = ref(false); // 标记是否已初始化

  // 树形数据状态
  const circuitTree = ref<TreeNode[]>([]);
  const originalCircuitTree = ref<TreeNode[]>([]); // 保存原始数据用于筛选
  const checkedCircuitIds = ref<string[]>([]);
  const expandedCircuitIds = ref<string[]>([]);
  const circuitFilterKeyword = ref<string>('');

  // 性能优化：搜索索引和缓存
  const searchIndex = new Map<string, Array<{ node: TreeNode; path: string[]; keywords: string[] }>>();
  const filterCache = new Map<string, TreeNode[]>();
  let dataVersion = 0;

  // 计算属性
  const enterpriseOptions = computed(() => {
    const options = enterprises.value.map((e) => ({
      label: e.label,
      value: e.value,
    }));

    if (process.env.NODE_ENV === 'development') {
      console.log('🔄 企业选项更新:', options.length, options);
    }

    return options;
  });

  const siteOptions = computed(() => {
    const options = sites.value.map((s) => ({
      label: s.name,
      value: s.id,
    }));

    if (process.env.NODE_ENV === 'development') {
      console.log('🔄 站点选项更新:', options.length, options);
    }

    return options;
  });

  const hasEnterprise = computed(() => !!selectedEnterpriseId.value);
  const hasSite = computed(() => !!selectedSiteId.value);

  // 树形数据计算属性（使用优化的筛选）
  const filteredCircuitTree = computed(() => {
    if (!circuitFilterKeyword.value.trim()) {
      return circuitTree.value;
    }
    return fastFilterTreeNodes(circuitFilterKeyword.value);
  });

  const selectedCircuitIds = computed(() => {
    return checkedCircuitIds.value.filter(id => {
      const node = findNodeById(circuitTree.value, id);
      return node?.type === 'circuit';
    });
  });

  const selectedCircuitNames = computed(() => {
    return selectedCircuitIds.value.map(id => {
      const node = findNodeById(circuitTree.value, id);
      return node?.label || '';
    }).filter(Boolean);
  });

  /**
   * 树形数据辅助函数（优化版本）
   */
  function findNodeById(nodes: TreeNode[], id: string): TreeNode | null {
    for (const node of nodes) {
      if (node.id === id) {
        return node;
      }
      if (node.children) {
        const found = findNodeById(node.children, id);
        if (found) return found;
      }
    }
    return null;
  }

  /**
   * 递归获取节点 ID
   */
  function getNodeIds(node: TreeNode, filter?: (node: TreeNode) => boolean): string[] {
    const ids: string[] = [];

    // 应用过滤条件
    if ((!filter || filter(node)) && node.id) ids.push(node.id);

    // 递归处理子节点
    if (node.children?.length) {
      for (const child of node.children) {
        ids.push(...getNodeIds(child, filter));
      }
    }

    return ids;
  }

  /**
   * 获取所有回路节点 ID（只包含 type 为 'circuit' 的节点）
   */
  function getCircuitIds(nodes: TreeNode[]): string[] {
    return nodes.flatMap((node) => getNodeIds(node, (n) => n.type === 'circuit'));
  }

  /**
   * 获取所有节点 ID（包括父节点和子节点，用于全选时确保父节点也被勾选）
   */
  function getAllNodeIds(nodes: TreeNode[]): string[] {
    return nodes.flatMap((node) => getNodeIds(node)); // 不使用过滤器，获取所有节点
  }

  /**
   * 验证并修复树数据
   */
  function validateTreeData(data: any[]): TreeNode[] {
    return data.map((node, index) => {
      // 确保必需字段存在
      if (!node.id) node.id = `node_${Date.now()}_${index}`;
      if (!node.label) node.label = `节点_${node.id}`;
      if (!node.type) node.type = 'group'; // 默认为分组类型

      // 递归处理子节点
      if (node.children?.length) {
        node.children = validateTreeData(node.children);
      }

      return node as TreeNode;
    });
  }

  /**
   * 创建搜索索引（性能优化）
   */
  function createSearchIndex(nodes: TreeNode[]): void {
    searchIndex.clear();
    dataVersion++;

    const traverse = (node: TreeNode, path: string[] = []) => {
      // 提取关键词（支持中文分词）
      const label = node.label || '';
      const keywords = label
        .toLowerCase()
        .split(/[\s\-_/\\]+/) // 按空格、横线、下划线、斜杠分割
        .filter(Boolean);

      // 为每个关键词建立索引
      keywords.forEach((keyword) => {
        if (!searchIndex.has(keyword)) {
          searchIndex.set(keyword, []);
        }
        searchIndex.get(keyword)!.push({
          node,
          path: [...path],
          keywords,
        });
      });

      // 递归处理子节点
      if (node.children?.length) {
        node.children.forEach((child) =>
          traverse(child, [...path, node.id]),
        );
      }
    };

    nodes.forEach((node) => traverse(node));
  }

  /**
   * 基于索引的快速筛选（性能优化）
   */
  function fastFilterTreeNodes(keyword: string): TreeNode[] {
    if (!keyword || !keyword.trim()) {
      return originalCircuitTree.value;
    }

    const cacheKey = `${keyword.toLowerCase().trim()}_${dataVersion}`;

    // 检查缓存
    if (filterCache.has(cacheKey)) {
      return filterCache.get(cacheKey)!;
    }

    const lowerKeyword = keyword.toLowerCase().trim();
    const matchedItems = new Set<TreeNode>();
    const expandedKeys: string[] = [];

    // 基于索引快速查找匹配项
    for (const [indexKeyword, items] of searchIndex.entries()) {
      if (indexKeyword.includes(lowerKeyword)) {
        items.forEach((item) => {
          matchedItems.add(item.node);
          // 自动展开父节点路径
          item.path.forEach((nodeId) => {
            if (!expandedKeys.includes(nodeId)) {
              expandedKeys.push(nodeId);
            }
          });
        });
      }
    }

    // 构建筛选后的树结构
    const result = buildFilteredTree(originalCircuitTree.value, matchedItems, expandedKeys);

    // 缓存结果
    filterCache.set(cacheKey, result);

    // 更新展开状态
    expandedCircuitIds.value = [
      ...new Set([...expandedKeys, ...expandedCircuitIds.value]),
    ];

    return result;
  }

  /**
   * 构建筛选后的树结构
   */
  function buildFilteredTree(
    nodes: TreeNode[],
    matchedItems: Set<TreeNode>,
    expandedKeys: string[],
  ): TreeNode[] {
    return nodes.reduce((filtered: TreeNode[], node) => {
      const nodeMatches = matchedItems.has(node);
      let filteredChildren: TreeNode[] = [];

      // 递归处理子节点
      if (node.children?.length) {
        filteredChildren = buildFilteredTree(node.children, matchedItems, expandedKeys);
      }

      // 如果当前节点匹配或有匹配的子节点，则保留该节点
      if (nodeMatches || filteredChildren.length > 0) {
        filtered.push({
          ...node,
          children: filteredChildren.length > 0 ? filteredChildren : node.children,
        });
      }

      return filtered;
    }, []);
  }

  /**
   * 初始化数据
   */
  const initializeData = async (
    options: {
      defaultEnterpriseId?: string;
      defaultSiteId?: string;
      force?: boolean; // 是否强制重新初始化
    } = {},
  ) => {
    // 如果已经初始化过且不是强制重新初始化，直接返回
    if (initialized.value && !options.force) {
      if (process.env.NODE_ENV === 'development') {
        console.log('📋 数据已初始化，跳过重复初始化');
      }
      return;
    }

    loading.value = true;
    error.value = '';

    try {
      if (process.env.NODE_ENV === 'development') {
        console.log('🚀 开始初始化筛选表单数据...');
      }

      // 1. 获取企业列表
      enterprises.value = await searchEnterprisesApi();
      if (process.env.NODE_ENV === 'development') {
        console.log('✅ 企业数据获取成功:', enterprises.value);
      }

      if (enterprises.value.length === 0) {
        throw new Error('没有可用的企业数据');
      }

      // 2. 确定要选择的企业
      let targetEnterpriseId = options.defaultEnterpriseId;

      if (targetEnterpriseId) {
        // 检查默认企业是否存在
        const enterpriseExists = enterprises.value.some(
          (e) => e.value === targetEnterpriseId,
        );
        if (!enterpriseExists) {
          console.warn('指定的默认企业不存在，将选择第一个企业');
          targetEnterpriseId = enterprises.value[0].value;
        }
      } else {
        // 没有指定默认值，选择第一个
        targetEnterpriseId = enterprises.value[0].value;
      }

      selectedEnterpriseId.value = targetEnterpriseId;

      if (process.env.NODE_ENV === 'development') {
        const selectedEnterprise = enterprises.value.find(
          (e) => e.value === targetEnterpriseId,
        );
        console.log('✅ 企业自动选择:', selectedEnterprise);
      }

      // 3. 获取该企业的站点列表
      await loadSitesForEnterprise(targetEnterpriseId, options.defaultSiteId);

      // 4. 标记初始化完成
      initialized.value = true;

      if (process.env.NODE_ENV === 'development') {
        console.log('✅ 数据初始化完成');
      }
    } catch (error_) {
      const errorMessage =
        error_ instanceof Error ? error_.message : '初始化数据失败';
      error.value = errorMessage;
      console.error('❌ 初始化数据失败:', error_);
    } finally {
      loading.value = false;
    }
  };

  /**
   * 为指定企业加载站点数据
   */
  const loadSitesForEnterprise = async (
    enterpriseId: string,
    defaultSiteId?: string,
  ) => {
    try {
      if (process.env.NODE_ENV === 'development') {
        console.log('🔄 获取企业站点数据:', enterpriseId);
      }

      sites.value = await getAllSitesApi(enterpriseId);

      if (process.env.NODE_ENV === 'development') {
        console.log('✅ 站点数据获取成功:', sites.value);
      }

      if (sites.value.length === 0) {
        selectedSiteId.value = '';
        console.warn('该企业下没有可用的站点');
        return;
      }

      // 确定要选择的站点
      let targetSiteId = defaultSiteId;

      if (targetSiteId) {
        // 检查默认站点是否存在且属于当前企业
        const siteExists = sites.value.some((s) => s.id === targetSiteId);
        if (!siteExists) {
          console.warn(
            '指定的默认站点不存在或不属于当前企业，将选择第一个站点',
          );
          targetSiteId = sites.value[0].id;
        }
      } else {
        // 没有指定默认值，选择第一个
        targetSiteId = sites.value[0].id;
      }

      selectedSiteId.value = targetSiteId;

      if (process.env.NODE_ENV === 'development') {
        const selectedSite = sites.value.find((s) => s.id === targetSiteId);
        console.log('✅ 站点自动选择:', selectedSite);
      }

      // 站点选择后，自动加载对应的回路树数据
      await loadCircuitTreeForSite(targetSiteId);
    } catch (error_) {
      console.error('❌ 获取站点数据失败:', error_);
      sites.value = [];
      selectedSiteId.value = '';
    }
  };

  /**
   * 为指定站点加载回路树数据（优化版本）
   */
  const loadCircuitTreeForSite = async (siteId: string) => {
    try {
      if (process.env.NODE_ENV === 'development') {
        console.log('🔄 获取站点回路树数据:', siteId);
      }

      const response = await getCircuitTreeApi({ siteId });
      const rawData = response.items || [];

      // 验证并修复数据
      const validatedData = validateTreeData(rawData);

      // 保存原始数据和当前数据
      originalCircuitTree.value = validatedData;
      circuitTree.value = validatedData;

      // 创建搜索索引（性能优化）
      createSearchIndex(validatedData);

      // 清空之前的状态
      checkedCircuitIds.value = [];
      expandedCircuitIds.value = [];
      circuitFilterKeyword.value = '';

      // 清空缓存
      filterCache.clear();

      if (process.env.NODE_ENV === 'development') {
        console.log('✅ 回路树数据获取成功:', validatedData.length, '个节点');
        console.log('✅ 搜索索引创建完成:', searchIndex.size, '个关键词');
      }
    } catch (error_) {
      console.error('❌ 获取回路树数据失败:', error_);
      circuitTree.value = [];
      originalCircuitTree.value = [];
      checkedCircuitIds.value = [];
      searchIndex.clear();
      filterCache.clear();
    }
  };

  /**
   * 处理企业变化
   */
  const handleEnterpriseChange = async (enterpriseId: string) => {
    if (selectedEnterpriseId.value === enterpriseId) {
      return; // 没有变化，不需要处理
    }

    if (process.env.NODE_ENV === 'development') {
      console.log('🔄 企业选择变化:', enterpriseId);
    }

    selectedEnterpriseId.value = enterpriseId;
    selectedSiteId.value = ''; // 清空站点选择

    if (enterpriseId) {
      await loadSitesForEnterprise(enterpriseId);
    } else {
      sites.value = [];
      selectedSiteId.value = '';
    }
  };

  /**
   * 处理站点变化
   */
  const handleSiteChange = async (siteId: string) => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🔄 站点选择变化:', siteId);
    }
    selectedSiteId.value = siteId;

    // 站点变化时，加载对应的回路树数据
    if (siteId) {
      await loadCircuitTreeForSite(siteId);
    } else {
      // 清空回路树数据
      circuitTree.value = [];
      checkedCircuitIds.value = [];
    }
  };

  /**
   * 获取当前选择的数据
   */
  const getCurrentSelection = () => {
    return {
      enterpriseId: selectedEnterpriseId.value,
      siteId: selectedSiteId.value,
      enterprise: enterprises.value.find(
        (e) => e.value === selectedEnterpriseId.value,
      ),
      site: sites.value.find((s) => s.id === selectedSiteId.value),
      circuitIds: selectedCircuitIds.value,
      circuitNames: selectedCircuitNames.value,
    };
  };

  /**
   * 树形节点操作方法
   */
  const handleCircuitNodeCheck = (checkedNodes: any, checkedInfo: any) => {
    checkedCircuitIds.value = checkedInfo?.checkedKeys || [];

    if (process.env.NODE_ENV === 'development') {
      console.log('🔄 回路节点选中变化:', {
        总选中: checkedCircuitIds.value.length,
        回路选中: selectedCircuitIds.value.length,
        回路名称: selectedCircuitNames.value,
      });
    }
  };

  const handleCircuitNodeExpand = (nodeData: any) => {
    if (!expandedCircuitIds.value.includes(nodeData.id)) {
      expandedCircuitIds.value.push(nodeData.id);
    }
  };

  const handleCircuitNodeCollapse = (nodeData: any) => {
    const index = expandedCircuitIds.value.indexOf(nodeData.id);
    if (index !== -1) {
      expandedCircuitIds.value.splice(index, 1);
    }
  };

  const setCircuitFilterKeyword = (keyword: string) => {
    circuitFilterKeyword.value = keyword;

    // 更新筛选后的树数据（通过计算属性自动触发）
    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 回路筛选关键词更新:', keyword);
    }
  };

  const clearCircuitSelection = () => {
    checkedCircuitIds.value = [];
  };

  /**
   * 全选/取消全选回路节点
   */
  const toggleSelectAllCircuits = (selectAll: boolean) => {
    if (selectAll) {
      // 获取所有回路节点ID
      checkedCircuitIds.value = getCircuitIds(filteredCircuitTree.value);
    } else {
      checkedCircuitIds.value = [];
    }

    if (process.env.NODE_ENV === 'development') {
      console.log('🔄 全选状态切换:', selectAll, '选中数量:', checkedCircuitIds.value.length);
    }
  };

  /**
   * 获取性能统计信息
   */
  const getPerformanceStats = () => {
    return {
      totalNodes: originalCircuitTree.value.length,
      filteredNodes: filteredCircuitTree.value.length,
      checkedNodes: checkedCircuitIds.value.length,
      circuitNodes: selectedCircuitIds.value.length,
      searchIndexSize: searchIndex.size,
      cacheSize: filterCache.size,
      dataVersion,
    };
  };

  /**
   * 重置数据
   */
  const reset = () => {
    enterprises.value = [];
    sites.value = [];
    selectedEnterpriseId.value = '';
    selectedSiteId.value = '';
    error.value = '';

    // 重置树形数据
    circuitTree.value = [];
    originalCircuitTree.value = [];
    checkedCircuitIds.value = [];
    expandedCircuitIds.value = [];
    circuitFilterKeyword.value = '';

    // 清理缓存和索引
    searchIndex.clear();
    filterCache.clear();
    dataVersion = 0;
  };

  return {
    // 只读数据
    enterprises: readonly(enterprises),
    sites: readonly(sites),
    selectedEnterpriseId: readonly(selectedEnterpriseId),
    selectedSiteId: readonly(selectedSiteId),
    loading: readonly(loading),
    error: readonly(error),
    initialized: readonly(initialized),

    // 树形数据
    circuitTree: readonly(circuitTree),
    filteredCircuitTree,
    checkedCircuitIds: readonly(checkedCircuitIds),
    expandedCircuitIds: readonly(expandedCircuitIds),
    circuitFilterKeyword: readonly(circuitFilterKeyword),

    // 计算属性
    enterpriseOptions,
    siteOptions,
    hasEnterprise,
    hasSite,
    selectedCircuitIds,
    selectedCircuitNames,

    // 方法
    initializeData,
    handleEnterpriseChange,
    handleSiteChange,
    getCurrentSelection,
    reset,

    // 树形节点方法
    handleCircuitNodeCheck,
    handleCircuitNodeExpand,
    handleCircuitNodeCollapse,
    setCircuitFilterKeyword,
    clearCircuitSelection,
    toggleSelectAllCircuits,

    // 工具方法
    getCircuitIds,
    getAllNodeIds,
    getPerformanceStats,
  };
}
