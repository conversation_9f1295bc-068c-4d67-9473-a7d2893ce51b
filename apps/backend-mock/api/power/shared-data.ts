/**
 * 电力系统共用数据定义
 * 用于各个电力相关报表页面的Mock数据生成
 */

// 企业数据类型定义
export interface Enterprise {
  id: string;
  name: string;
}

// 站点数据类型定义
export interface Site {
  id: string;
  name: string;
  enterpriseId: string;
}

// 回路数据类型定义
export interface Circuit {
  name: string;
  id: string;
  siteId: string;
}

/**
 * 统一的企业数据（使用真实的企业ID格式）
 */
export const enterprises: Enterprise[] = [
  { id: '91110000MA001234XY', name: '华能电力集团' }, // 统一社会信用代码格式
  { id: '91110000MA005678AB', name: '国家电网公司' },
  { id: '91110000MA009012CD', name: '南方电网公司' },
  { id: '91110000MA003456EF', name: '中国电建集团' },
  { id: '91110000MA007890GH', name: '大唐发电集团' },
];

/**
 * 统一的站点数据（使用新的企业ID格式）
 */
export const sites: Site[] = [
  { id: '1', name: '北京变电站', enterpriseId: '91110000MA001234XY' },
  { id: '2', name: '上海变电站', enterpriseId: '91110000MA005678AB' },
  { id: '3', name: '广州变电站', enterpriseId: '91110000MA009012CD' },
  { id: '4', name: '深圳变电站', enterpriseId: '91110000MA005678AB' },
  { id: '5', name: '成都变电站', enterpriseId: '91110000MA003456EF' },
  { id: '6', name: '天津变电站', enterpriseId: '91110000MA001234XY' },
  { id: '7', name: '重庆变电站', enterpriseId: '91110000MA007890GH' },
  { id: '8', name: '杭州变电站', enterpriseId: '91110000MA005678AB' },
];

/**
 * 回路数据（每个回路关联到特定站点）
 */
export const circuits: Circuit[] = [
  { name: '1#主变高压侧', id: 'circuit_001', siteId: '1' },
  { name: '1#主变低压侧', id: 'circuit_002', siteId: '1' },
  { name: '2#主变高压侧', id: 'circuit_003', siteId: '2' },
  { name: '2#主变低压侧', id: 'circuit_004', siteId: '2' },
  { name: '3#主变高压侧', id: 'circuit_005', siteId: '3' },
  { name: '3#主变低压侧', id: 'circuit_006', siteId: '3' },
  { name: '1#进线柜', id: 'circuit_007', siteId: '1' },
  { name: '2#进线柜', id: 'circuit_008', siteId: '2' },
  { name: '3#进线柜', id: 'circuit_009', siteId: '3' },
  { name: '母联柜A', id: 'circuit_010', siteId: '1' },
  { name: '母联柜B', id: 'circuit_011', siteId: '2' },
  { name: '母联柜C', id: 'circuit_012', siteId: '3' },
  { name: '1#出线柜', id: 'circuit_013', siteId: '4' },
  { name: '2#出线柜', id: 'circuit_014', siteId: '4' },
  { name: '3#出线柜', id: 'circuit_015', siteId: '5' },
  { name: '1#配电柜', id: 'circuit_016', siteId: '1' },
  { name: '2#配电柜', id: 'circuit_017', siteId: '2' },
  { name: '1#计量柜', id: 'circuit_018', siteId: '3' },
  { name: '2#计量柜', id: 'circuit_019', siteId: '4' },
  { name: '1#补偿柜', id: 'circuit_020', siteId: '5' },
];

/**
 * 根据站点ID获取该站点下的所有回路
 */
export function getCircuitsBySiteId(siteId: string): Circuit[] {
  return circuits.filter(circuit => circuit.siteId === siteId);
}

/**
 * 根据企业ID获取该企业下的所有站点
 */
export function getSitesByEnterpriseId(enterpriseId: string): Site[] {
  return sites.filter(site => site.enterpriseId === enterpriseId);
}

/**
 * 根据企业ID获取该企业下的所有回路
 */
export function getCircuitsByEnterpriseId(enterpriseId: string): Circuit[] {
  const enterpriseSites = getSitesByEnterpriseId(enterpriseId);
  const siteIds = enterpriseSites.map(site => site.id);
  return circuits.filter(circuit => siteIds.includes(circuit.siteId));
}
