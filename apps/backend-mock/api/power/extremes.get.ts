import { useResponseSuccess } from '~/utils/response';

// 电力极值报表数据类型定义
interface PowerExtremesData {
  circuitName: string; // 回路名称
  date: number; // 日期时间戳
  // 有功功率(kW)
  activePowerMaxValue: number; // 最大值数值
  activePowerMaxTime: number; // 最大值发生时间戳
  activePowerMinValue: number; // 最小值数值
  activePowerMinTime: number; // 最小值发生时间戳
  activePowerAvgValue: number; // 平均值
  // 无功功率(kVar)
  reactivePowerMaxValue: number; // 最大值数值
  reactivePowerMaxTime: number; // 最大值发生时间戳
  reactivePowerMinValue: number; // 最小值数值
  reactivePowerMinTime: number; // 最小值发生时间戳
  reactivePowerAvgValue: number; // 平均值
  // 视在功率(kVA)
  apparentPowerMaxValue: number; // 最大值数值
  apparentPowerMaxTime: number; // 最大值发生时间戳
  apparentPowerMinValue: number; // 最小值数值
  apparentPowerMinTime: number; // 最小值发生时间戳
  apparentPowerAvgValue: number; // 平均值
  // 功率因数
  powerFactorMaxValue: number; // 最大值数值
  powerFactorMaxTime: number; // 最大值发生时间戳
  powerFactorMinValue: number; // 最小值数值
  powerFactorMinTime: number; // 最小值发生时间戳
  powerFactorAvgValue: number; // 平均值
}

/**
 * 生成随机功率数据
 */
function generateRandomPowerData(baseValue: number, variance: number = 0.3, baseDate: Date) {
  const maxValue = baseValue * (1 + Math.random() * variance);
  const minValue = baseValue * (1 - Math.random() * variance);
  const avgValue = (maxValue + minValue) / 2;

  // 生成随机时间戳（当天的随机时间）
  const maxTime = new Date(baseDate);
  maxTime.setHours(Math.floor(Math.random() * 24), Math.floor(Math.random() * 60), 0, 0);

  const minTime = new Date(baseDate);
  minTime.setHours(Math.floor(Math.random() * 24), Math.floor(Math.random() * 60), 0, 0);

  return {
    maxValue: Number(maxValue.toFixed(2)),
    minValue: Number(minValue.toFixed(2)),
    avgValue: Number(avgValue.toFixed(2)),
    maxTime: maxTime.getTime(), // 返回时间戳
    minTime: minTime.getTime(), // 返回时间戳
  };
}

/**
 * 生成随机功率因数数据
 */
function generateRandomPowerFactorData(baseDate: Date) {
  const baseValue = 0.85 + Math.random() * 0.1; // 0.85-0.95之间
  const maxValue = Math.min(1.0, baseValue + Math.random() * 0.05);
  const minValue = Math.max(0.7, baseValue - Math.random() * 0.1);
  const avgValue = (maxValue + minValue) / 2;

  // 生成随机时间戳（当天的随机时间）
  const maxTime = new Date(baseDate);
  maxTime.setHours(Math.floor(Math.random() * 24), Math.floor(Math.random() * 60), 0, 0);

  const minTime = new Date(baseDate);
  minTime.setHours(Math.floor(Math.random() * 24), Math.floor(Math.random() * 60), 0, 0);

  return {
    maxValue: Number(maxValue.toFixed(3)),
    minValue: Number(minValue.toFixed(3)),
    avgValue: Number(avgValue.toFixed(3)),
    maxTime: maxTime.getTime(), // 返回时间戳
    minTime: minTime.getTime(), // 返回时间戳
  };
}

/**
 * 根据筛选条件生成Mock数据
 */
function generateMockData(params: {
  startTime?: string;
  endTime?: string;
  reportType?: string;
  powerType?: string;
  count?: number;
}): PowerExtremesData[] {
  const { startTime, endTime, reportType = 'dayly', powerType = '1', count = 15 } = params;

  const circuits = [
    '1#主变高压侧', '1#主变低压侧', '2#主变高压侧', '2#主变低压侧',
    '3#主变高压侧', '3#主变低压侧', '4#主变高压侧', '4#主变低压侧',
    '1#进线柜', '2#进线柜', '3#进线柜', '4#进线柜', '5#进线柜', '6#进线柜',
    '母联柜A', '母联柜B', '母联柜C', '备用母联柜',
    '1#出线柜', '2#出线柜', '3#出线柜', '4#出线柜', '5#出线柜', '6#出线柜',
    '7#出线柜', '8#出线柜', '9#出线柜', '10#出线柜', '11#出线柜', '12#出线柜',
    '1#配电柜', '2#配电柜', '3#配电柜', '4#配电柜', '5#配电柜', '6#配电柜',
    '1#计量柜', '2#计量柜', '3#计量柜', '4#计量柜',
    '1#补偿柜', '2#补偿柜', '3#补偿柜', '4#补偿柜',
    '备用进线A', '备用进线B', '备用进线C',
    '应急电源A', '应急电源B', 'UPS电源A', 'UPS电源B',
    '照明配电箱', '动力配电箱', '空调配电箱', '消防配电箱',
  ];

  const data: PowerExtremesData[] = [];

  // 根据时间范围生成数据
  let dates: Date[] = [];

  if (startTime && endTime) {
    // 使用前端传入的时间范围
    const start = new Date(startTime);
    const end = new Date(endTime);

    if (reportType === 'monthly') {
      // 月报：生成40条数据，每条代表不同的回路在同一月份的数据
      const baseDate = new Date(start);
      baseDate.setDate(1); // 设置为月初
      for (let i = 0; i < count; i++) {
        dates.push(new Date(baseDate));
      }
    } else if (reportType === 'custom') {
      // 自定义报表：生成50条数据，分布在时间范围内
      const totalDays = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
      const interval = Math.max(1, Math.floor(totalDays / count));

      for (let i = 0; i < count; i++) {
        const date = new Date(start);
        date.setDate(date.getDate() + (i * interval));
        if (date <= end) {
          dates.push(date);
        } else {
          dates.push(new Date(end));
        }
      }
    } else {
      // 日报：生成20条数据，都是同一天但不同回路
      const baseDate = new Date(startTime);
      for (let i = 0; i < count; i++) {
        dates.push(new Date(baseDate));
      }
    }
  } else {
    // 没有时间范围时，生成默认数据
    for (let i = 0; i < count; i++) {
      let date: Date;
      if (reportType === 'monthly') {
        // 月报：同一个月的不同回路
        date = new Date();
        date.setDate(1);
      } else if (reportType === 'custom') {
        // 自定义：分散在最近50天
        date = new Date();
        date.setDate(date.getDate() - i);
      } else {
        // 日报：同一天的不同回路
        date = new Date();
      }
      dates.push(date);
    }
  }

  // 根据电力类别调整基础数值
  const powerTypeMultipliers = {
    '1': { base: 1500, variance: 0.4 }, // 功率
    '2': { base: 800, variance: 0.3 },  // 电流
    '3': { base: 380, variance: 0.2 },  // 相电压
    '4': { base: 660, variance: 0.2 },  // 线电压
    '5': { base: 5, variance: 0.5 },    // 不平衡度
    '6': { base: 3, variance: 0.6 },    // 电压谐波
    '7': { base: 4, variance: 0.6 },    // 电流谐波
  };

  const multiplier = powerTypeMultipliers[powerType as keyof typeof powerTypeMultipliers] || powerTypeMultipliers['1'];

  dates.forEach((date, index) => {
    // 生成各种功率数据（根据电力类别调整数值）
    const activePower = generateRandomPowerData(multiplier.base, multiplier.variance, date);
    const reactivePower = generateRandomPowerData(multiplier.base * 0.6, multiplier.variance, date);
    const apparentPower = generateRandomPowerData(multiplier.base * 1.1, multiplier.variance * 0.8, date);
    const powerFactor = generateRandomPowerFactorData(date);

    data.push({
      circuitName: circuits[index % circuits.length],
      date: date.getTime(), // 返回时间戳
      // 有功功率
      activePowerMaxValue: activePower.maxValue,
      activePowerMaxTime: activePower.maxTime,
      activePowerMinValue: activePower.minValue,
      activePowerMinTime: activePower.minTime,
      activePowerAvgValue: activePower.avgValue,
      // 无功功率
      reactivePowerMaxValue: reactivePower.maxValue,
      reactivePowerMaxTime: reactivePower.maxTime,
      reactivePowerMinValue: reactivePower.minValue,
      reactivePowerMinTime: reactivePower.minTime,
      reactivePowerAvgValue: reactivePower.avgValue,
      // 视在功率
      apparentPowerMaxValue: apparentPower.maxValue,
      apparentPowerMaxTime: apparentPower.maxTime,
      apparentPowerMinValue: apparentPower.minValue,
      apparentPowerMinTime: apparentPower.minTime,
      apparentPowerAvgValue: apparentPower.avgValue,
      // 功率因数
      powerFactorMaxValue: powerFactor.maxValue,
      powerFactorMaxTime: powerFactor.maxTime,
      powerFactorMinValue: powerFactor.minValue,
      powerFactorMinTime: powerFactor.minTime,
      powerFactorAvgValue: powerFactor.avgValue,
    });
  });

  return data;
}

export default defineEventHandler(async (event) => {
  console.log('Power extremes API called');

  // 获取查询参数
  const query = getQuery(event);
  const {
    startTime,
    endTime,
    reportType = 'dayly',
    powerType = '1',
    circuitName,
    page = 1,
    pageSize = 50
  } = query;

  console.log('Query params:', { startTime, endTime, reportType, powerType, circuitName, page, pageSize });

  // 根据报表类型确定数据条数
  const dataCountMap = {
    'dayly': 20,    // 日报：20条
    'monthly': 40,  // 月报：40条
    'custom': 50,   // 自定义：50条
  };

  const dataCount = dataCountMap[String(reportType) as keyof typeof dataCountMap] || 20;
  console.log(`Report type: ${reportType}, generating ${dataCount} items`);

  // 根据筛选条件生成Mock数据
  let mockData = generateMockData({
    startTime: String(startTime || ''),
    endTime: String(endTime || ''),
    reportType: String(reportType),
    powerType: String(powerType),
    count: dataCount,
  });

  console.log(`Generated ${mockData.length} items for reportType: ${reportType} (expected: ${dataCount}), powerType: ${powerType}`);

  // 显示生成的数据样本（显示前3条和最后1条）
  if (mockData.length > 0) {
    console.log('Sample data (first 3 items):');
    mockData.slice(0, 3).forEach((item, index) => {
      console.log(`  ${index + 1}. ${item.circuitName} - ${new Date(item.date).toISOString().split('T')[0]} - Power: ${item.activePowerMaxValue}`);
    });

    if (mockData.length > 3) {
      const lastItem = mockData[mockData.length - 1];
      console.log(`  ... (${mockData.length - 3} more items)`);
      console.log(`  ${mockData.length}. ${lastItem.circuitName} - ${new Date(lastItem.date).toISOString().split('T')[0]} - Power: ${lastItem.activePowerMaxValue}`);
    }
  }

  // 根据回路名称过滤
  if (circuitName && String(circuitName).trim()) {
    const searchKeyword = String(circuitName).toLowerCase().trim();
    console.log('Filtering circuits with keyword:', searchKeyword);
    mockData = mockData.filter(item =>
      item.circuitName.toLowerCase().includes(searchKeyword)
    );
  }

  // 根据时间范围过滤（简单模拟）
  if (startTime && endTime) {
    console.log('Filtering by date range:', { startTime, endTime });
    // 这里可以根据实际需求实现时间过滤逻辑
  }

  // 分页处理
  const pageNum = Number(page);
  const pageSizeNum = Number(pageSize);
  const startIndex = (pageNum - 1) * pageSizeNum;
  const endIndex = startIndex + pageSizeNum;
  const paginatedData = mockData.slice(startIndex, endIndex);

  console.log('Mock data length:', mockData.length);
  console.log('Paginated data length:', paginatedData.length);

  // 根据电力类别添加元数据
  const powerTypeInfo = {
    '1': { name: '功率', unit: 'kW/kVar/kVA' },
    '2': { name: '电流', unit: 'A' },
    '3': { name: '相电压', unit: 'V' },
    '4': { name: '线电压', unit: 'V' },
    '5': { name: '不平衡度', unit: '%' },
    '6': { name: '电压谐波', unit: '%' },
    '7': { name: '电流谐波', unit: '%' },
  };

  const currentPowerType = powerTypeInfo[String(powerType) as keyof typeof powerTypeInfo] || powerTypeInfo['1'];

  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 300));

  return useResponseSuccess({
    items: paginatedData,
    total: mockData.length,
    page: pageNum,
    pageSize: pageSizeNum,
    reportType,
    powerType: String(powerType),
    powerTypeName: currentPowerType.name,
    powerTypeUnit: currentPowerType.unit,
    expectedDataCount: dataCount, // 预期生成的数据条数
    actualDataCount: mockData.length, // 实际生成的数据条数
    dataCountInfo: {
      dayly: 20,
      monthly: 40,
      custom: 50,
    },
    filterConditions: {
      startTime: String(startTime || ''),
      endTime: String(endTime || ''),
      reportType: String(reportType),
      powerType: String(powerType),
      circuitName: String(circuitName || ''),
    },
  });
});
