import { defineEventHandler, readBody } from 'h3';
import { useResponseSuccess } from '~/utils/response';
import { sites } from './shared-data';

// 网关状态数据类型定义
interface GatewayStatusData {
  stationName: string; // 变配电站名称
  gateway: string; // 网关
  serialPort: string; // 串口号
  meterAddress: string; // 仪表地址
  meterName: string; // 仪表名称
  switchStatus: number; // 开关状态：1-开启，0-关闭
  commStatus: number; // 通讯状态：1-在线，0-离线
  offlineTime: number; // 离线时间（时间戳）
  lastCollectTime: number; // 最后采集时间（时间戳）
  totalInterruptTime: number; // 累计中断时间（时间戳）
}

/**
 * 请求参数结构
 */
interface RequestParams {
  query?: {
    enterpriseId?: string;
    siteId?: string;
    keyword?: string;
    commLossOnly?: boolean;
  };
  pagination: {
    page: number;
    pageSize: number;
  };
}

/**
 * 生成网关状态Mock数据
 */
function generateGatewayStatusMockData(): GatewayStatusData[] {
  const data: GatewayStatusData[] = [];
  const now = new Date();

  // 仪表类型
  const meterTypes = [
    '电能表', '电流表', '电压表', '功率表', '温度表',
    '湿度表', '压力表', '流量表', '液位表', '开关量'
  ];

  // 开关状态：1-开启，0-关闭
  const switchStatuses = [1, 0];

  // 通讯状态：1-在线，0-离线
  const commStatuses = [1, 0];

  // 为每个站点生成多个网关设备
  sites.forEach((site, siteIndex) => {
    // 每个站点生成5-10个设备
    const deviceCount = 5 + Math.floor(Math.random() * 6);

    for (let deviceIndex = 0; deviceIndex < deviceCount; deviceIndex++) {
      // 生成网关（1-5号网关）
      const gateway = `网关${Math.floor(Math.random() * 5) + 1}`;

      // 生成串口号（COM1-COM20）
      const serialPort = `COM${Math.floor(Math.random() * 20) + 1}`;

      // 生成仪表地址（16进制格式）
      const meterAddress = `0x${(siteIndex * 100 + deviceIndex + 1).toString(16).toUpperCase().padStart(4, '0')}`;

      // 生成仪表名称
      const meterType = meterTypes[deviceIndex % meterTypes.length];
      const meterName = `${site.name}-${gateway}-${meterType}`;

      // 随机生成状态
      const switchStatus = switchStatuses[Math.floor(Math.random() * switchStatuses.length)];
      const commStatus = commStatuses[Math.floor(Math.random() * commStatuses.length)];

      // 生成时间相关数据
      const lastCollectTime = now.getTime() - Math.random() * 24 * 60 * 60 * 1000; // 最近24小时内

      // 离线时间：如果通讯状态是离线，则有离线时间
      let offlineTime = 0;
      if (commStatus === 0) {
        offlineTime = now.getTime() - Math.random() * 12 * 60 * 60 * 1000; // 最近12小时内开始离线
      }

      // 累计中断时间：随机生成（以毫秒为单位）
      const totalInterruptTime = Math.random() * 48 * 60 * 60 * 1000; // 0-48小时

      data.push({
        stationName: site.name,
        gateway,
        serialPort,
        meterAddress,
        meterName,
        switchStatus,
        commStatus,
        offlineTime,
        lastCollectTime,
        totalInterruptTime,
      });
    }
  });

  console.log('🔍 生成网关状态数据:', {
    totalDevices: data.length,
    stationsCount: sites.length,
    sampleData: data.slice(0, 2)
  });

  return data;
}

export default defineEventHandler(async (event) => {
  console.log('网关状态API调用 (POST)');
  console.log('🔍 服务器当前时间:', new Date().toISOString());

  // 获取POST请求体
  const requestBody: RequestParams = await readBody(event);

  const { query = {}, pagination } = requestBody;
  const {
    enterpriseId = '',
    siteId = '',
    keyword = '',
    commLossOnly = false
  } = query;

  const { page = 1, pageSize = 50 } = pagination;

  console.log('🔍 查询参数:', { enterpriseId, siteId, keyword, commLossOnly });

  // 生成完整的基础数据
  let mockData = generateGatewayStatusMockData();

  console.log('🔍 初始数据量:', mockData.length);

  // 根据站点ID过滤
  if (siteId && siteId.trim()) {
    const selectedSite = sites.find(site => site.id === siteId);
    if (selectedSite) {
      mockData = mockData.filter(item =>
        item.stationName === selectedSite.name
      );
      console.log('🔍 站点筛选后数据量:', mockData.length);
    }
  }

  // 根据企业ID过滤（如果没有指定站点，则按企业过滤）
  if (enterpriseId && enterpriseId.trim() && (!siteId || !siteId.trim())) {
    const enterpriseSites = sites.filter(site => site.enterpriseId === enterpriseId);
    const enterpriseSiteNames = enterpriseSites.map(site => site.name);
    mockData = mockData.filter(item =>
      enterpriseSiteNames.includes(item.stationName)
    );
    console.log('🔍 企业筛选后数据量:', mockData.length);
  }

  // 根据关键字过滤（搜索仪表名称、仪表地址）
  if (keyword && keyword.trim()) {
    const searchKeyword = keyword.toLowerCase().trim();
    mockData = mockData.filter(item =>
      item.meterName.toLowerCase().includes(searchKeyword) ||
      item.meterAddress.toLowerCase().includes(searchKeyword)
    );
    console.log('🔍 关键字筛选后数据量:', mockData.length);
  }

  // 根据"只显示通讯中断"过滤
  if (commLossOnly) {
    mockData = mockData.filter(item => item.commStatus === 0); // 0表示离线/中断
    console.log('🔍 通讯中断筛选后数据量:', mockData.length);
  }

  // 分页处理
  const pageNum = Number(page);
  const pageSizeNum = Number(pageSize);
  const startIndex = (pageNum - 1) * pageSizeNum;
  const endIndex = startIndex + pageSizeNum;
  const paginatedData = mockData.slice(startIndex, endIndex);

  console.log('🔍 分页后数据量:', paginatedData.length);

  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 300));

  return useResponseSuccess({
    items: paginatedData,
    total: mockData.length,
    page: pageNum,
    pageSize: pageSizeNum,
    enterpriseId: String(enterpriseId),
    siteId: String(siteId),
    keyword: String(keyword),
    commLossOnly: Boolean(commLossOnly),
  });
});
