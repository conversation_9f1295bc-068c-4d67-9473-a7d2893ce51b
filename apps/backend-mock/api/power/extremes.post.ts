import { defineEventHandler, readBody } from 'h3';
import { useResponseSuccess } from '~/utils/response';
import { enterprises, sites, circuits } from './shared-data';

// 电力极值报表数据类型定义
interface PowerExtremesData {
  // 显示字段
  circuitName: string; // 回路名称
  date: number; // 日期时间戳
  // 有功功率(kW)
  activePowerMaxValue: number; // 最大值数值
  activePowerMaxTime: number; // 最大值发生时间戳
  activePowerMinValue: number; // 最小值数值
  activePowerMinTime: number; // 最小值发生时间戳
  activePowerAvgValue: number; // 平均值
  // 无功功率(kVar)
  reactivePowerMaxValue: number; // 最大值数值
  reactivePowerMaxTime: number; // 最大值发生时间戳
  reactivePowerMinValue: number; // 最小值数值
  reactivePowerMinTime: number; // 最小值发生时间戳
  reactivePowerAvgValue: number; // 平均值
  // 视在功率(kVA)
  apparentPowerMaxValue: number; // 最大值数值
  apparentPowerMaxTime: number; // 最大值发生时间戳
  apparentPowerMinValue: number; // 最小值数值
  apparentPowerMinTime: number; // 最小值发生时间戳
  apparentPowerAvgValue: number; // 平均值
  // 功率因数
  powerFactorMaxValue: number; // 最大值数值
  powerFactorMaxTime: number; // 最大值发生时间戳
  powerFactorMinValue: number; // 最小值数值
  powerFactorMinTime: number; // 最小值发生时间戳
  powerFactorAvgValue: number; // 平均值

  // 筛选字段（不在表格中显示，但用于过滤）
  enterpriseId: string; // 企业ID
  energyType: string; // 能源类型
  siteId: string; // 站点ID
  circuitId: string; // 回路ID
  electricalCategory: string; // 电力类别
}

/**
 * 请求参数结构
 */
interface RequestParams {
  query: {
    startTime?: string;
    endTime?: string;
    reportType?: string;
    electricalCategory?: string;
    enterpriseId?: string;
    energyType?: string;
    siteId?: string;
    keyword?: string;
    circuitNames?: string[];
  };
  pagination: {
    page: number;
    pageSize: number;
  };
}

/**
 * 生成随机功率数据
 */
function generateRandomPowerData(baseValue: number, variance: number = 0.3, baseDate: Date) {
  const maxValue = baseValue * (1 + Math.random() * variance);
  const minValue = baseValue * (1 - Math.random() * variance);
  const avgValue = (maxValue + minValue) / 2;

  // 生成随机时间戳（当天的随机时间）
  const maxTime = new Date(baseDate);
  maxTime.setHours(Math.floor(Math.random() * 24), Math.floor(Math.random() * 60), 0, 0);

  const minTime = new Date(baseDate);
  minTime.setHours(Math.floor(Math.random() * 24), Math.floor(Math.random() * 60), 0, 0);

  return {
    maxValue: Number(maxValue.toFixed(2)),
    minValue: Number(minValue.toFixed(2)),
    avgValue: Number(avgValue.toFixed(2)),
    maxTime: maxTime.getTime(),
    minTime: minTime.getTime(),
  };
}

/**
 * 生成随机功率因数数据
 */
function generateRandomPowerFactorData(baseDate: Date) {
  const maxValue = 0.85 + Math.random() * 0.15; // 0.85-1.0
  const minValue = 0.70 + Math.random() * 0.15; // 0.70-0.85
  const avgValue = (maxValue + minValue) / 2;

  const maxTime = new Date(baseDate);
  maxTime.setHours(Math.floor(Math.random() * 24), Math.floor(Math.random() * 60), 0, 0);

  const minTime = new Date(baseDate);
  minTime.setHours(Math.floor(Math.random() * 24), Math.floor(Math.random() * 60), 0, 0);

  return {
    maxValue: Number(maxValue.toFixed(3)),
    minValue: Number(minValue.toFixed(3)),
    avgValue: Number(avgValue.toFixed(3)),
    maxTime: maxTime.getTime(),
    minTime: minTime.getTime(),
  };
}

/**
 * 生成完整的基础Mock数据（不根据报表类型区分，统一生成完整数据）
 */
function generateBaseMockData(params: {
  electricalCategory?: string;
  energyType?: string;
}): PowerExtremesData[] {
  const {
    electricalCategory = '1',
    energyType = '1',
  } = params;



  // 使用共用的企业、站点、回路数据

  const data: PowerExtremesData[] = [];

  // 生成完整的基础数据（基于当前用户时间及往前30天）
  const dates: Date[] = [];
  const now = new Date();

  // 生成从今天往前30天的数据（包含今天，共31天）
  for (let dayOffset = 0; dayOffset <= 30; dayOffset++) {
    const date = new Date(now);
    date.setDate(date.getDate() - dayOffset);
    // 设置为当天的中午12:00:00
    date.setHours(12, 0, 0, 0);
    dates.push(date);
  }

  console.log('🔍 生成数据时间范围（基于当前时间）:', {
    today: now.toISOString().split('T')[0],
    startDate: dates[dates.length - 1].toISOString().split('T')[0], // 最早日期
    endDate: dates[0].toISOString().split('T')[0], // 最晚日期（今天）
    totalDays: dates.length
  });

  console.log('🔍 生成的日期样本:', dates.slice(0, 5).map(d => d.toISOString().split('T')[0]));

  // 预览每天的数据量分布
  const dailyDataCount = dates.slice(0, 5).map(date => {
    const dateString = date.toISOString().split('T')[0];
    const dateHash = dateString.split('').reduce((hash, char) => hash + char.charCodeAt(0), 0);
    const circuitCount = (dateHash % 16) + 5;
    return { date: dateString, circuitCount };
  });
  console.log('🔍 每日数据量预览:', dailyDataCount);

  // 根据电力类别调整基础数值
  const powerTypeMultipliers = {
    '1': { base: 1500, variance: 0.4 }, // 功率
    '2': { base: 800, variance: 0.3 },  // 电流
    '3': { base: 380, variance: 0.2 },  // 相电压
    '4': { base: 660, variance: 0.2 },  // 线电压
    '5': { base: 5, variance: 0.5 },    // 不平衡度
    '6': { base: 3, variance: 0.6 },    // 电压谐波
    '7': { base: 4, variance: 0.6 },    // 电流谐波
  };

  const multiplier = powerTypeMultipliers[electricalCategory as keyof typeof powerTypeMultipliers] || powerTypeMultipliers['1'];

  // 为每个日期生成随机数量的回路数据
  dates.forEach(date => {
    // 基于日期生成稳定的随机数（同一天查询多次结果一致）
    const dateString = date.toISOString().split('T')[0]; // 获取日期字符串 YYYY-MM-DD
    const dateHash = dateString.split('').reduce((hash, char) => hash + char.charCodeAt(0), 0);
    const seededRandom = (dateHash % 16) + 5; // 基于日期哈希生成5-20之间的数字

    // 为每个日期选择固定数量的回路
    const selectedCircuits = circuits.slice(0, seededRandom);

    selectedCircuits.forEach(circuit => {
      const site = sites.find(s => s.id === circuit.siteId);
      const enterprise = enterprises.find(e => e.id === site?.enterpriseId);

      const activePower = generateRandomPowerData(multiplier.base, multiplier.variance, date);
      const reactivePower = generateRandomPowerData(multiplier.base * 0.6, multiplier.variance, date);
      const apparentPower = generateRandomPowerData(multiplier.base * 1.1, multiplier.variance * 0.8, date);
      const powerFactor = generateRandomPowerFactorData(date);

      data.push({
        // 显示字段
        circuitName: circuit.name,
        date: date.getTime(),
        // 有功功率
        activePowerMaxValue: activePower.maxValue,
        activePowerMaxTime: activePower.maxTime,
        activePowerMinValue: activePower.minValue,
        activePowerMinTime: activePower.minTime,
        activePowerAvgValue: activePower.avgValue,
        // 无功功率
        reactivePowerMaxValue: reactivePower.maxValue,
        reactivePowerMaxTime: reactivePower.maxTime,
        reactivePowerMinValue: reactivePower.minValue,
        reactivePowerMinTime: reactivePower.minTime,
        reactivePowerAvgValue: reactivePower.avgValue,
        // 视在功率
        apparentPowerMaxValue: apparentPower.maxValue,
        apparentPowerMaxTime: apparentPower.maxTime,
        apparentPowerMinValue: apparentPower.minValue,
        apparentPowerMinTime: apparentPower.minTime,
        apparentPowerAvgValue: apparentPower.avgValue,
        // 功率因数
        powerFactorMaxValue: powerFactor.maxValue,
        powerFactorMaxTime: powerFactor.maxTime,
        powerFactorMinValue: powerFactor.minValue,
        powerFactorMinTime: powerFactor.minTime,
        powerFactorAvgValue: powerFactor.avgValue,

        // 筛选字段（不显示但用于过滤）
        enterpriseId: enterprise?.id || '',
        energyType: energyType || '1', // 能源类型
        siteId: site?.id || '',
        circuitId: circuit.id,
        electricalCategory: electricalCategory || '1', // 电力类别
      });
    });
  });

  return data;
}

export default defineEventHandler(async (event) => {
  // 获取POST请求体
  const requestBody: RequestParams = await readBody(event);

  const { query, pagination } = requestBody;
  const {
    startTime,
    endTime,
    reportType = 'dayly',
    electricalCategory = '1',
    enterpriseId = '',
    energyType = '',
    siteId = '',
    keyword = '',
    circuitNames = []
  } = query;

  const { page = 1, pageSize = 50 } = pagination;



  // 生成完整的基础数据（不区分报表类型）
  let mockData = generateBaseMockData({
    electricalCategory: String(electricalCategory),
    energyType: String(energyType),
  });

  console.log('🔍 初始数据量:', mockData.length);
  console.log('🔍 查询参数:', { startTime, endTime, enterpriseId, siteId, electricalCategory });

  // 根据筛选条件过滤数据

  // 1. 根据时间范围筛选（重要筛选条件）
  if (startTime && endTime) {
    // 处理日期字符串，确保正确的时间范围比较
    const startDate = new Date(startTime);
    const endDate = new Date(endTime);

    // 设置开始时间为当天的 00:00:00
    startDate.setHours(0, 0, 0, 0);
    // 设置结束时间为当天的 23:59:59
    endDate.setHours(23, 59, 59, 999);

    const startTimestamp = startDate.getTime();
    const endTimestamp = endDate.getTime();

    console.log('🔍 时间筛选:', {
      startTime,
      endTime,
      startTimestamp,
      endTimestamp,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString()
    });

    const beforeFilter = mockData.length;
    console.log('🔍 筛选前数据样本:', mockData.slice(0, 3).map(item => ({
      circuitName: item.circuitName,
      date: new Date(item.date).toISOString(),
      timestamp: item.date
    })));

    mockData = mockData.filter(item => {
      const itemDate = item.date;
      const isInRange = itemDate >= startTimestamp && itemDate <= endTimestamp;
      return isInRange;
    });

    console.log('🔍 时间筛选后数据量:', mockData.length, '(原:', beforeFilter, ')');
    if (mockData.length > 0) {
      console.log('🔍 筛选后数据样本:', mockData.slice(0, 3).map(item => ({
        circuitName: item.circuitName,
        date: new Date(item.date).toISOString()
      })));
    }
  }

  // 2. 根据电力类别筛选（重要筛选条件）
  if (electricalCategory && String(electricalCategory).trim()) {
    mockData = mockData.filter(item => item.electricalCategory === String(electricalCategory));
  }

  // 3. 根据企业筛选
  if (enterpriseId && String(enterpriseId).trim()) {
    mockData = mockData.filter(item => item.enterpriseId === String(enterpriseId));
  }

  // 4. 根据能源类型筛选
  if (energyType && String(energyType).trim()) {
    mockData = mockData.filter(item => item.energyType === String(energyType));
  }

  // 5. 根据站点筛选
  if (siteId && String(siteId).trim()) {
    mockData = mockData.filter(item => item.siteId === String(siteId));
  }

  // 6. 根据选中的回路ID筛选
  if (circuitNames && circuitNames.length > 0) {
    mockData = mockData.filter(item => circuitNames.includes(item.circuitId));
  }

  // 7. 根据关键字过滤（支持回路名称筛选）
  if (keyword && String(keyword).trim()) {
    const searchKeyword = String(keyword).toLowerCase().trim();
    mockData = mockData.filter(item =>
      item.circuitName.toLowerCase().includes(searchKeyword)
    );
  }

  // 分页处理
  const pageNum = Number(page);
  const pageSizeNum = Number(pageSize);
  const startIndex = (pageNum - 1) * pageSizeNum;
  const endIndex = startIndex + pageSizeNum;
  const paginatedData = mockData.slice(startIndex, endIndex);



  // 根据电力类别添加元数据
  const powerTypeInfo = {
    '1': { name: '功率', unit: 'kW/kVar/kVA' },
    '2': { name: '电流', unit: 'A' },
    '3': { name: '相电压', unit: 'V' },
    '4': { name: '线电压', unit: 'V' },
    '5': { name: '不平衡度', unit: '%' },
    '6': { name: '电压谐波', unit: '%' },
    '7': { name: '电流谐波', unit: '%' },
  };

  const currentPowerType = powerTypeInfo[String(electricalCategory) as keyof typeof powerTypeInfo] || powerTypeInfo['1'];

  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 300));

  // 返回页面必要的字段：items、total、page、pageSize、reportType、electricalCategory、时间范围
  return useResponseSuccess({
    items: paginatedData,
    total: mockData.length,
    page: pageNum,
    pageSize: pageSizeNum,
    reportType,
    electricalCategory: String(electricalCategory), // 保留电力类别筛选条件
    startTime: String(startTime || ''), // 开始时间
    endTime: String(endTime || ''), // 结束时间
  });
});
