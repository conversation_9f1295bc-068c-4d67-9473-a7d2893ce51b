import { define<PERSON><PERSON><PERSON><PERSON><PERSON>, getQ<PERSON>y } from 'h3';

import { useResponseSuccess } from '~/utils/response';

// 生成所有回路名称列表
function generateCircuitNames(): { label: string; value: string }[] {
  return [
    // 主变压器回路
    { label: '1#主变高压侧', value: 'circuit_transformer_1_high' },
    { label: '1#主变低压侧', value: 'circuit_transformer_1_low' },
    { label: '2#主变高压侧', value: 'circuit_transformer_2_high' },
    { label: '2#主变低压侧', value: 'circuit_transformer_2_low' },
    { label: '3#主变高压侧', value: 'circuit_transformer_3_high' },
    { label: '3#主变低压侧', value: 'circuit_transformer_3_low' },
    { label: '4#主变高压侧', value: 'circuit_transformer_4_high' },
    { label: '4#主变低压侧', value: 'circuit_transformer_4_low' },

    // 进线柜回路
    { label: '1#进线柜', value: 'circuit_incoming_1' },
    { label: '2#进线柜', value: 'circuit_incoming_2' },
    { label: '3#进线柜', value: 'circuit_incoming_3' },
    { label: '4#进线柜', value: 'circuit_incoming_4' },
    { label: '5#进线柜', value: 'circuit_incoming_5' },
    { label: '6#进线柜', value: 'circuit_incoming_6' },

    // 出线柜回路
    { label: '1#出线柜', value: 'circuit_outgoing_1' },
    { label: '2#出线柜', value: 'circuit_outgoing_2' },
    { label: '3#出线柜', value: 'circuit_outgoing_3' },
    { label: '4#出线柜', value: 'circuit_outgoing_4' },
    { label: '5#出线柜', value: 'circuit_outgoing_5' },
    { label: '6#出线柜', value: 'circuit_outgoing_6' },
    { label: '7#出线柜', value: 'circuit_outgoing_7' },
    { label: '8#出线柜', value: 'circuit_outgoing_8' },
    { label: '9#出线柜', value: 'circuit_outgoing_9' },
    { label: '10#出线柜', value: 'circuit_outgoing_10' },
    { label: '11#出线柜', value: 'circuit_outgoing_11' },
    { label: '12#出线柜', value: 'circuit_outgoing_12' },

    // 配电柜回路
    { label: '1#配电柜', value: 'circuit_power_1' },
    { label: '2#配电柜', value: 'circuit_power_2' },
    { label: '3#配电柜', value: 'circuit_power_3' },
    { label: '4#配电柜', value: 'circuit_power_4' },
    { label: '5#配电柜', value: 'circuit_power_5' },
    { label: '6#配电柜', value: 'circuit_power_6' },

    // 母联柜回路
    { label: '母联柜A', value: 'circuit_direct_1' },
    { label: '母联柜B', value: 'circuit_direct_2' },
    { label: '母联柜C', value: 'circuit_direct_3' },
    { label: '备用母联柜', value: 'circuit_direct_4' },

    // 计量柜回路
    { label: '1#计量柜', value: 'circuit_metering_1' },
    { label: '2#计量柜', value: 'circuit_metering_2' },
    { label: '3#计量柜', value: 'circuit_metering_3' },
    { label: '4#计量柜', value: 'circuit_metering_4' },

    // 补偿柜回路
    { label: '1#补偿柜', value: 'circuit_compensation_1' },
    { label: '2#补偿柜', value: 'circuit_compensation_2' },
    { label: '3#补偿柜', value: 'circuit_compensation_3' },
    { label: '4#补偿柜', value: 'circuit_compensation_4' },

    // 备用进线回路
    { label: '备用进线A', value: 'circuit_backup_incoming_a' },
    { label: '备用进线B', value: 'circuit_backup_incoming_b' },
    { label: '备用进线C', value: 'circuit_backup_incoming_c' },

    // 应急电源回路
    { label: '应急电源A', value: 'circuit_emergency_a' },
    { label: '应急电源B', value: 'circuit_emergency_b' },
    { label: 'UPS电源A', value: 'circuit_ups_a' },
    { label: 'UPS电源B', value: 'circuit_ups_b' },

    // 专用配电箱回路
    { label: '照明配电箱', value: 'circuit_lighting_box' },
    { label: '动力配电箱', value: 'circuit_power_box' },
    { label: '空调配电箱', value: 'circuit_ac_box' },
    { label: '消防配电箱', value: 'circuit_fire_box' },
  ];
}

export default defineEventHandler(async (event) => {
  console.log('Circuit names API called');

  // 获取查询参数
  const query = getQuery(event);
  const { keyword } = query;

  console.log('Query params:', { keyword });

  // 生成所有回路名称
  let circuitNames = generateCircuitNames();

  // 根据关键词过滤
  if (keyword && String(keyword).trim()) {
    const searchKeyword = String(keyword).toLowerCase().trim();
    console.log('Filtering circuit names with keyword:', searchKeyword);

    circuitNames = circuitNames.filter(circuit =>
      circuit.label.toLowerCase().includes(searchKeyword)
    );
  }

  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 150));

  console.log(`Generated ${circuitNames.length} circuit names`);

  return useResponseSuccess({
    items: circuitNames,
    total: circuitNames.length,
  });
});
