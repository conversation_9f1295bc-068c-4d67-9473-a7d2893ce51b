import { defineEvent<PERSON>and<PERSON>, getQuery } from 'h3';

import { useResponseSuccess } from '~/utils/response';

// 回路树节点数据类型
interface CircuitTreeNode {
  id: string;
  label: string;
  parentId?: string | null;
  level: number;
  type: 'group' | 'circuit';
  status?: number;
  sort?: number;
  children?: CircuitTreeNode[];
}

// 为不同站点生成不同的回路树数据
function generateCircuitTreeDataBySite(siteId?: string): CircuitTreeNode[] {
  console.log('🔍 为站点生成回路树数据:', siteId);

  // 根据站点ID返回不同的回路配置
  switch (siteId) {
    case '1': // 北京变电站
      return generateBeijingStationCircuits();
    case '2': // 上海变电站
      return generateShanghaiStationCircuits();
    case '3': // 广州变电站
      return generateGuangzhouStationCircuits();
    case '4': // 深圳变电站
      return generateShenzhenStationCircuits();
    case '5': // 成都变电站
      return generateChengduStationCircuits();
    case '6': // 天津变电站
      return generateTianjinStationCircuits();
    case '7': // 重庆变电站
      return generateChongqingStationCircuits();
    case '8': // 杭州变电站
      return generateHangzhouStationCircuits();
    default:
      // 默认返回通用配置
      return generateDefaultCircuits();
  }
}

// 北京变电站回路配置（大型站点，回路较多）
function generateBeijingStationCircuits(): CircuitTreeNode[] {
  return [
    // 主变压器回路组
    {
      id: 'group_main_transformer_bj',
      label: '主变压器回路',
      parentId: null,
      level: 1,
      type: 'group',
      status: 1,
      sort: 1,
      children: [
        { id: 'circuit_transformer_1_high_bj', label: '1#主变高压侧', parentId: 'group_main_transformer_bj', level: 2, type: 'circuit', status: 1, sort: 1 },
        { id: 'circuit_transformer_1_low_bj', label: '1#主变低压侧', parentId: 'group_main_transformer_bj', level: 2, type: 'circuit', status: 1, sort: 2 },
        { id: 'circuit_transformer_2_high_bj', label: '2#主变高压侧', parentId: 'group_main_transformer_bj', level: 2, type: 'circuit', status: 1, sort: 3 },
        { id: 'circuit_transformer_2_low_bj', label: '2#主变低压侧', parentId: 'group_main_transformer_bj', level: 2, type: 'circuit', status: 1, sort: 4 },
        { id: 'circuit_transformer_3_high_bj', label: '3#主变高压侧', parentId: 'group_main_transformer_bj', level: 2, type: 'circuit', status: 1, sort: 5 },
        { id: 'circuit_transformer_3_low_bj', label: '3#主变低压侧', parentId: 'group_main_transformer_bj', level: 2, type: 'circuit', status: 1, sort: 6 },
      ],
    },
    // 进线柜回路组
    {
      id: 'group_incoming_bj',
      label: '进线柜回路',
      parentId: null,
      level: 1,
      type: 'group',
      status: 1,
      sort: 2,
      children: [
        { id: 'circuit_incoming_1_bj', label: '1#进线柜', parentId: 'group_incoming_bj', level: 2, type: 'circuit', status: 1, sort: 1 },
        { id: 'circuit_incoming_2_bj', label: '2#进线柜', parentId: 'group_incoming_bj', level: 2, type: 'circuit', status: 1, sort: 2 },
        { id: 'circuit_incoming_3_bj', label: '3#进线柜', parentId: 'group_incoming_bj', level: 2, type: 'circuit', status: 1, sort: 3 },
        { id: 'circuit_incoming_4_bj', label: '4#进线柜', parentId: 'group_incoming_bj', level: 2, type: 'circuit', status: 1, sort: 4 },
      ],
    },
    // 出线柜回路组
    {
      id: 'group_outgoing_bj',
      label: '出线柜回路',
      parentId: null,
      level: 1,
      type: 'group',
      status: 1,
      sort: 3,
      children: [
        { id: 'circuit_outgoing_1_bj', label: '1#出线柜', parentId: 'group_outgoing_bj', level: 2, type: 'circuit', status: 1, sort: 1 },
        { id: 'circuit_outgoing_2_bj', label: '2#出线柜', parentId: 'group_outgoing_bj', level: 2, type: 'circuit', status: 1, sort: 2 },
        { id: 'circuit_outgoing_3_bj', label: '3#出线柜', parentId: 'group_outgoing_bj', level: 2, type: 'circuit', status: 1, sort: 3 },
        { id: 'circuit_outgoing_4_bj', label: '4#出线柜', parentId: 'group_outgoing_bj', level: 2, type: 'circuit', status: 1, sort: 4 },
        { id: 'circuit_outgoing_5_bj', label: '5#出线柜', parentId: 'group_outgoing_bj', level: 2, type: 'circuit', status: 1, sort: 5 },
        { id: 'circuit_outgoing_6_bj', label: '6#出线柜', parentId: 'group_outgoing_bj', level: 2, type: 'circuit', status: 1, sort: 6 },
      ],
    },
    // 母联柜（独立回路）
    { id: 'circuit_bus_tie_1_bj', label: '1#母联柜', parentId: null, level: 1, type: 'circuit', status: 1, sort: 4 },
    { id: 'circuit_bus_tie_2_bj', label: '2#母联柜', parentId: null, level: 1, type: 'circuit', status: 1, sort: 5 },
  ];
}

// 上海变电站回路配置（中等规模）
function generateShanghaiStationCircuits(): CircuitTreeNode[] {
  return [
    // 主变压器回路组
    {
      id: 'group_main_transformer_sh',
      label: '主变压器回路',
      parentId: null,
      level: 1,
      type: 'group',
      status: 1,
      sort: 1,
      children: [
        { id: 'circuit_transformer_1_high_sh', label: '1#主变高压侧', parentId: 'group_main_transformer_sh', level: 2, type: 'circuit', status: 1, sort: 1 },
        { id: 'circuit_transformer_1_low_sh', label: '1#主变低压侧', parentId: 'group_main_transformer_sh', level: 2, type: 'circuit', status: 1, sort: 2 },
        { id: 'circuit_transformer_2_high_sh', label: '2#主变高压侧', parentId: 'group_main_transformer_sh', level: 2, type: 'circuit', status: 1, sort: 3 },
        { id: 'circuit_transformer_2_low_sh', label: '2#主变低压侧', parentId: 'group_main_transformer_sh', level: 2, type: 'circuit', status: 1, sort: 4 },
      ],
    },
    // 配电柜回路组
    {
      id: 'group_distribution_sh',
      label: '配电柜回路',
      parentId: null,
      level: 1,
      type: 'group',
      status: 1,
      sort: 2,
      children: [
        { id: 'circuit_distribution_1_sh', label: '1#配电柜', parentId: 'group_distribution_sh', level: 2, type: 'circuit', status: 1, sort: 1 },
        { id: 'circuit_distribution_2_sh', label: '2#配电柜', parentId: 'group_distribution_sh', level: 2, type: 'circuit', status: 1, sort: 2 },
        { id: 'circuit_distribution_3_sh', label: '3#配电柜', parentId: 'group_distribution_sh', level: 2, type: 'circuit', status: 1, sort: 3 },
      ],
    },
    // 进线柜回路组
    {
      id: 'group_incoming_sh',
      label: '进线柜回路',
      parentId: null,
      level: 1,
      type: 'group',
      status: 1,
      sort: 3,
      children: [
        { id: 'circuit_incoming_1_sh', label: '1#进线柜', parentId: 'group_incoming_sh', level: 2, type: 'circuit', status: 1, sort: 1 },
        { id: 'circuit_incoming_2_sh', label: '2#进线柜', parentId: 'group_incoming_sh', level: 2, type: 'circuit', status: 1, sort: 2 },
        { id: 'circuit_incoming_3_sh', label: '3#进线柜', parentId: 'group_incoming_sh', level: 2, type: 'circuit', status: 1, sort: 3 },
      ],
    },
    // 母联柜（独立回路）
    { id: 'circuit_bus_tie_sh', label: '母联柜', parentId: null, level: 1, type: 'circuit', status: 1, sort: 4 },
  ];
}

// 广州变电站回路配置（小规模）
function generateGuangzhouStationCircuits(): CircuitTreeNode[] {
  return [
    // 主变压器回路组
    {
      id: 'group_main_transformer_gz',
      label: '主变压器回路',
      parentId: null,
      level: 1,
      type: 'group',
      status: 1,
      sort: 1,
      children: [
        { id: 'circuit_transformer_1_high_gz', label: '1#主变高压侧', parentId: 'group_main_transformer_gz', level: 2, type: 'circuit', status: 1, sort: 1 },
        { id: 'circuit_transformer_1_low_gz', label: '1#主变低压侧', parentId: 'group_main_transformer_gz', level: 2, type: 'circuit', status: 1, sort: 2 },
      ],
    },
    // 配电柜回路组
    {
      id: 'group_distribution_gz',
      label: '配电柜回路',
      parentId: null,
      level: 1,
      type: 'group',
      status: 1,
      sort: 2,
      children: [
        { id: 'circuit_distribution_1_gz', label: '1#配电柜', parentId: 'group_distribution_gz', level: 2, type: 'circuit', status: 1, sort: 1 },
        { id: 'circuit_distribution_2_gz', label: '2#配电柜', parentId: 'group_distribution_gz', level: 2, type: 'circuit', status: 1, sort: 2 },
      ],
    },
    // 进线柜回路
    { id: 'circuit_incoming_gz', label: '进线柜', parentId: null, level: 1, type: 'circuit', status: 1, sort: 3 },
    // 母联柜
    { id: 'circuit_bus_tie_gz', label: '母联柜', parentId: null, level: 1, type: 'circuit', status: 1, sort: 4 },
  ];
}

// 其他站点的简化配置
function generateShenzhenStationCircuits(): CircuitTreeNode[] {
  return [
    { id: 'circuit_transformer_sz', label: '主变压器', parentId: null, level: 1, type: 'circuit', status: 1, sort: 1 },
    { id: 'circuit_incoming_1_sz', label: '1#进线柜', parentId: null, level: 1, type: 'circuit', status: 1, sort: 2 },
    { id: 'circuit_incoming_2_sz', label: '2#进线柜', parentId: null, level: 1, type: 'circuit', status: 1, sort: 3 },
    { id: 'circuit_distribution_sz', label: '配电柜', parentId: null, level: 1, type: 'circuit', status: 1, sort: 4 },
    { id: 'circuit_bus_tie_sz', label: '母联柜', parentId: null, level: 1, type: 'circuit', status: 1, sort: 5 },
  ];
}

function generateChengduStationCircuits(): CircuitTreeNode[] {
  return [
    { id: 'circuit_transformer_cd', label: '主变压器', parentId: null, level: 1, type: 'circuit', status: 1, sort: 1 },
    { id: 'circuit_incoming_cd', label: '进线柜', parentId: null, level: 1, type: 'circuit', status: 1, sort: 2 },
    { id: 'circuit_distribution_1_cd', label: '1#配电柜', parentId: null, level: 1, type: 'circuit', status: 1, sort: 3 },
    { id: 'circuit_distribution_2_cd', label: '2#配电柜', parentId: null, level: 1, type: 'circuit', status: 1, sort: 4 },
    { id: 'circuit_distribution_3_cd', label: '3#配电柜', parentId: null, level: 1, type: 'circuit', status: 1, sort: 5 },
    { id: 'circuit_bus_tie_cd', label: '母联柜', parentId: null, level: 1, type: 'circuit', status: 1, sort: 6 },
  ];
}

function generateTianjinStationCircuits(): CircuitTreeNode[] {
  return [
    { id: 'circuit_transformer_1_tj', label: '1#主变压器', parentId: null, level: 1, type: 'circuit', status: 1, sort: 1 },
    { id: 'circuit_transformer_2_tj', label: '2#主变压器', parentId: null, level: 1, type: 'circuit', status: 1, sort: 2 },
    { id: 'circuit_incoming_tj', label: '进线柜', parentId: null, level: 1, type: 'circuit', status: 1, sort: 3 },
    { id: 'circuit_distribution_tj', label: '配电柜', parentId: null, level: 1, type: 'circuit', status: 1, sort: 4 },
  ];
}

function generateChongqingStationCircuits(): CircuitTreeNode[] {
  return [
    { id: 'circuit_transformer_cq', label: '主变压器', parentId: null, level: 1, type: 'circuit', status: 1, sort: 1 },
    { id: 'circuit_incoming_1_cq', label: '1#进线柜', parentId: null, level: 1, type: 'circuit', status: 1, sort: 2 },
    { id: 'circuit_incoming_2_cq', label: '2#进线柜', parentId: null, level: 1, type: 'circuit', status: 1, sort: 3 },
    { id: 'circuit_incoming_3_cq', label: '3#进线柜', parentId: null, level: 1, type: 'circuit', status: 1, sort: 4 },
    { id: 'circuit_distribution_cq', label: '配电柜', parentId: null, level: 1, type: 'circuit', status: 1, sort: 5 },
    { id: 'circuit_bus_tie_cq', label: '母联柜', parentId: null, level: 1, type: 'circuit', status: 1, sort: 6 },
    { id: 'circuit_compensation_cq', label: '补偿柜', parentId: null, level: 1, type: 'circuit', status: 1, sort: 7 },
  ];
}

function generateHangzhouStationCircuits(): CircuitTreeNode[] {
  return [
    { id: 'circuit_transformer_hz', label: '主变压器', parentId: null, level: 1, type: 'circuit', status: 1, sort: 1 },
    { id: 'circuit_incoming_hz', label: '进线柜', parentId: null, level: 1, type: 'circuit', status: 1, sort: 2 },
    { id: 'circuit_distribution_hz', label: '配电柜', parentId: null, level: 1, type: 'circuit', status: 1, sort: 3 },
  ];
}

// 默认通用配置
function generateDefaultCircuits(): CircuitTreeNode[] {
  return [
    { id: 'circuit_default_1', label: '默认回路1', parentId: null, level: 1, type: 'circuit', status: 1, sort: 1 },
    { id: 'circuit_default_2', label: '默认回路2', parentId: null, level: 1, type: 'circuit', status: 1, sort: 2 },
    { id: 'circuit_default_3', label: '默认回路3', parentId: null, level: 1, type: 'circuit', status: 1, sort: 3 },
  ];
}

// 原有的通用回路数据（保留作为备用）
function generateOriginalCircuitTreeData(): CircuitTreeNode[] {
  const treeData: CircuitTreeNode[] = [
    // 一级结构：直接是回路节点（母联柜等独立回路）
    {
      id: 'circuit_direct_1',
      label: '母联柜A',
      parentId: null,
      level: 1,
      type: 'circuit',
      status: 1,
      sort: 1,
    },
    {
      id: 'circuit_direct_2',
      label: '母联柜B',
      parentId: null,
      level: 1,
      type: 'circuit',
      status: 1,
      sort: 2,
    },
    {
      id: 'circuit_direct_3',
      label: '母联柜C',
      parentId: null,
      level: 1,
      type: 'circuit',
      status: 1,
      sort: 3,
    },
    {
      id: 'circuit_direct_4',
      label: '备用母联柜',
      parentId: null,
      level: 1,
      type: 'circuit',
      status: 1,
      sort: 4,
    },

    // 二级结构：分组 -> 回路
    {
      id: 'group_power_distribution',
      label: '配电柜回路',
      parentId: null,
      level: 1,
      type: 'group',
      status: 1,
      sort: 5,
      children: [
        {
          id: 'circuit_power_1',
          label: '1#配电柜',
          parentId: 'group_power_distribution',
          level: 2,
          type: 'circuit',
          status: 1,
          sort: 1,
        },
        {
          id: 'circuit_power_2',
          label: '2#配电柜',
          parentId: 'group_power_distribution',
          level: 2,
          type: 'circuit',
          status: 1,
          sort: 2,
        },
        {
          id: 'circuit_power_3',
          label: '3#配电柜',
          parentId: 'group_power_distribution',
          level: 2,
          type: 'circuit',
          status: 1,
          sort: 3,
        },
        {
          id: 'circuit_power_4',
          label: '4#配电柜',
          parentId: 'group_power_distribution',
          level: 2,
          type: 'circuit',
          status: 1,
          sort: 4,
        },
        {
          id: 'circuit_power_5',
          label: '5#配电柜',
          parentId: 'group_power_distribution',
          level: 2,
          type: 'circuit',
          status: 1,
          sort: 5,
        },
        {
          id: 'circuit_power_6',
          label: '6#配电柜',
          parentId: 'group_power_distribution',
          level: 2,
          type: 'circuit',
          status: 1,
          sort: 6,
        },
      ],
    },

    // 二级结构：进线柜回路
    {
      id: 'group_incoming',
      label: '进线柜回路',
      parentId: null,
      level: 1,
      type: 'group',
      status: 1,
      sort: 6,
      children: [
        {
          id: 'circuit_incoming_1',
          label: '1#进线柜',
          parentId: 'group_incoming',
          level: 2,
          type: 'circuit',
          status: 1,
          sort: 1,
        },
        {
          id: 'circuit_incoming_2',
          label: '2#进线柜',
          parentId: 'group_incoming',
          level: 2,
          type: 'circuit',
          status: 1,
          sort: 2,
        },
        {
          id: 'circuit_incoming_3',
          label: '3#进线柜',
          parentId: 'group_incoming',
          level: 2,
          type: 'circuit',
          status: 1,
          sort: 3,
        },
        {
          id: 'circuit_incoming_4',
          label: '4#进线柜',
          parentId: 'group_incoming',
          level: 2,
          type: 'circuit',
          status: 1,
          sort: 4,
        },
        {
          id: 'circuit_incoming_5',
          label: '5#进线柜',
          parentId: 'group_incoming',
          level: 2,
          type: 'circuit',
          status: 1,
          sort: 5,
        },
        {
          id: 'circuit_incoming_6',
          label: '6#进线柜',
          parentId: 'group_incoming',
          level: 2,
          type: 'circuit',
          status: 1,
          sort: 6,
        },
      ],
    },

    // 三级结构：主变压器回路
    {
      id: 'group_transformer',
      label: '主变压器回路',
      parentId: null,
      level: 1,
      type: 'group',
      status: 1,
      sort: 7,
      children: [
        {
          id: 'group_transformer_1',
          label: '1#主变',
          parentId: 'group_transformer',
          level: 2,
          type: 'group',
          status: 1,
          sort: 1,
          children: [
            {
              id: 'circuit_transformer_1_high',
              label: '1#主变高压侧',
              parentId: 'group_transformer_1',
              level: 3,
              type: 'circuit',
              status: 1,
              sort: 1,
            },
            {
              id: 'circuit_transformer_1_low',
              label: '1#主变低压侧',
              parentId: 'group_transformer_1',
              level: 3,
              type: 'circuit',
              status: 1,
              sort: 2,
            },
          ],
        },
        {
          id: 'group_transformer_2',
          label: '2#主变',
          parentId: 'group_transformer',
          level: 2,
          type: 'group',
          status: 1,
          sort: 2,
          children: [
            {
              id: 'circuit_transformer_2_high',
              label: '2#主变高压侧',
              parentId: 'group_transformer_2',
              level: 3,
              type: 'circuit',
              status: 1,
              sort: 1,
            },
            {
              id: 'circuit_transformer_2_low',
              label: '2#主变低压侧',
              parentId: 'group_transformer_2',
              level: 3,
              type: 'circuit',
              status: 1,
              sort: 2,
            },
          ],
        },
        {
          id: 'group_transformer_3',
          label: '3#主变',
          parentId: 'group_transformer',
          level: 2,
          type: 'group',
          status: 1,
          sort: 3,
          children: [
            {
              id: 'circuit_transformer_3_high',
              label: '3#主变高压侧',
              parentId: 'group_transformer_3',
              level: 3,
              type: 'circuit',
              status: 1,
              sort: 1,
            },
            {
              id: 'circuit_transformer_3_low',
              label: '3#主变低压侧',
              parentId: 'group_transformer_3',
              level: 3,
              type: 'circuit',
              status: 1,
              sort: 2,
            },
          ],
        },
        {
          id: 'group_transformer_4',
          label: '4#主变',
          parentId: 'group_transformer',
          level: 2,
          type: 'group',
          status: 1,
          sort: 4,
          children: [
            {
              id: 'circuit_transformer_4_high',
              label: '4#主变高压侧',
              parentId: 'group_transformer_4',
              level: 3,
              type: 'circuit',
              status: 1,
              sort: 1,
            },
            {
              id: 'circuit_transformer_4_low',
              label: '4#主变低压侧',
              parentId: 'group_transformer_4',
              level: 3,
              type: 'circuit',
              status: 1,
              sort: 2,
            },
          ],
        },
      ],
    },

    // 三级结构：出线柜回路
    {
      id: 'group_outgoing',
      label: '出线柜回路',
      parentId: null,
      level: 1,
      type: 'group',
      status: 1,
      sort: 8,
      children: [
        {
          id: 'group_outgoing_a',
          label: 'A段出线',
          parentId: 'group_outgoing',
          level: 2,
          type: 'group',
          status: 1,
          sort: 1,
          children: [
            {
              id: 'circuit_outgoing_1',
              label: '1#出线柜',
              parentId: 'group_outgoing_a',
              level: 3,
              type: 'circuit',
              status: 1,
              sort: 1,
            },
            {
              id: 'circuit_outgoing_2',
              label: '2#出线柜',
              parentId: 'group_outgoing_a',
              level: 3,
              type: 'circuit',
              status: 1,
              sort: 2,
            },
            {
              id: 'circuit_outgoing_3',
              label: '3#出线柜',
              parentId: 'group_outgoing_a',
              level: 3,
              type: 'circuit',
              status: 1,
              sort: 3,
            },
            {
              id: 'circuit_outgoing_4',
              label: '4#出线柜',
              parentId: 'group_outgoing_a',
              level: 3,
              type: 'circuit',
              status: 1,
              sort: 4,
            },
            {
              id: 'circuit_outgoing_5',
              label: '5#出线柜',
              parentId: 'group_outgoing_a',
              level: 3,
              type: 'circuit',
              status: 1,
              sort: 5,
            },
            {
              id: 'circuit_outgoing_6',
              label: '6#出线柜',
              parentId: 'group_outgoing_a',
              level: 3,
              type: 'circuit',
              status: 1,
              sort: 6,
            },
          ],
        },
        {
          id: 'group_outgoing_b',
          label: 'B段出线',
          parentId: 'group_outgoing',
          level: 2,
          type: 'group',
          status: 1,
          sort: 2,
          children: [
            {
              id: 'circuit_outgoing_7',
              label: '7#出线柜',
              parentId: 'group_outgoing_b',
              level: 3,
              type: 'circuit',
              status: 1,
              sort: 1,
            },
            {
              id: 'circuit_outgoing_8',
              label: '8#出线柜',
              parentId: 'group_outgoing_b',
              level: 3,
              type: 'circuit',
              status: 1,
              sort: 2,
            },
            {
              id: 'circuit_outgoing_9',
              label: '9#出线柜',
              parentId: 'group_outgoing_b',
              level: 3,
              type: 'circuit',
              status: 1,
              sort: 3,
            },
            {
              id: 'circuit_outgoing_10',
              label: '10#出线柜',
              parentId: 'group_outgoing_b',
              level: 3,
              type: 'circuit',
              status: 1,
              sort: 4,
            },
            {
              id: 'circuit_outgoing_11',
              label: '11#出线柜',
              parentId: 'group_outgoing_b',
              level: 3,
              type: 'circuit',
              status: 1,
              sort: 5,
            },
            {
              id: 'circuit_outgoing_12',
              label: '12#出线柜',
              parentId: 'group_outgoing_b',
              level: 3,
              type: 'circuit',
              status: 1,
              sort: 6,
            },
          ],
        },
      ],
    },

    // 二级结构：专用设备回路
    {
      id: 'group_special_equipment',
      label: '专用设备回路',
      parentId: null,
      level: 1,
      type: 'group',
      status: 1,
      sort: 9,
      children: [
        {
          id: 'circuit_metering_1',
          label: '1#计量柜',
          parentId: 'group_special_equipment',
          level: 2,
          type: 'circuit',
          status: 1,
          sort: 1,
        },
        {
          id: 'circuit_metering_2',
          label: '2#计量柜',
          parentId: 'group_special_equipment',
          level: 2,
          type: 'circuit',
          status: 1,
          sort: 2,
        },
        {
          id: 'circuit_metering_3',
          label: '3#计量柜',
          parentId: 'group_special_equipment',
          level: 2,
          type: 'circuit',
          status: 1,
          sort: 3,
        },
        {
          id: 'circuit_metering_4',
          label: '4#计量柜',
          parentId: 'group_special_equipment',
          level: 2,
          type: 'circuit',
          status: 1,
          sort: 4,
        },
        {
          id: 'circuit_compensation_1',
          label: '1#补偿柜',
          parentId: 'group_special_equipment',
          level: 2,
          type: 'circuit',
          status: 1,
          sort: 5,
        },
        {
          id: 'circuit_compensation_2',
          label: '2#补偿柜',
          parentId: 'group_special_equipment',
          level: 2,
          type: 'circuit',
          status: 1,
          sort: 6,
        },
        {
          id: 'circuit_compensation_3',
          label: '3#补偿柜',
          parentId: 'group_special_equipment',
          level: 2,
          type: 'circuit',
          status: 1,
          sort: 7,
        },
        {
          id: 'circuit_compensation_4',
          label: '4#补偿柜',
          parentId: 'group_special_equipment',
          level: 2,
          type: 'circuit',
          status: 1,
          sort: 8,
        },
      ],
    },

    // 二级结构：应急电源回路
    {
      id: 'group_emergency',
      label: '应急电源回路',
      parentId: null,
      level: 1,
      type: 'group',
      status: 1,
      sort: 10,
      children: [
        {
          id: 'circuit_backup_incoming_a',
          label: '备用进线A',
          parentId: 'group_emergency',
          level: 2,
          type: 'circuit',
          status: 1,
          sort: 1,
        },
        {
          id: 'circuit_backup_incoming_b',
          label: '备用进线B',
          parentId: 'group_emergency',
          level: 2,
          type: 'circuit',
          status: 1,
          sort: 2,
        },
        {
          id: 'circuit_backup_incoming_c',
          label: '备用进线C',
          parentId: 'group_emergency',
          level: 2,
          type: 'circuit',
          status: 1,
          sort: 3,
        },
        {
          id: 'circuit_emergency_a',
          label: '应急电源A',
          parentId: 'group_emergency',
          level: 2,
          type: 'circuit',
          status: 1,
          sort: 4,
        },
        {
          id: 'circuit_emergency_b',
          label: '应急电源B',
          parentId: 'group_emergency',
          level: 2,
          type: 'circuit',
          status: 1,
          sort: 5,
        },
        {
          id: 'circuit_ups_a',
          label: 'UPS电源A',
          parentId: 'group_emergency',
          level: 2,
          type: 'circuit',
          status: 1,
          sort: 6,
        },
        {
          id: 'circuit_ups_b',
          label: 'UPS电源B',
          parentId: 'group_emergency',
          level: 2,
          type: 'circuit',
          status: 1,
          sort: 7,
        },
      ],
    },

    // 一级结构：配电箱回路（直接回路）
    {
      id: 'circuit_lighting_box',
      label: '照明配电箱',
      parentId: null,
      level: 1,
      type: 'circuit',
      status: 1,
      sort: 11,
    },
    {
      id: 'circuit_power_box',
      label: '动力配电箱',
      parentId: null,
      level: 1,
      type: 'circuit',
      status: 1,
      sort: 12,
    },
    {
      id: 'circuit_ac_box',
      label: '空调配电箱',
      parentId: null,
      level: 1,
      type: 'circuit',
      status: 1,
      sort: 13,
    },
    {
      id: 'circuit_fire_box',
      label: '消防配电箱',
      parentId: null,
      level: 1,
      type: 'circuit',
      status: 1,
      sort: 14,
    },
  ];

  return treeData;
}

export default defineEventHandler(async (event) => {
  console.log('Circuit tree API called');

  // 获取查询参数
  const query = getQuery(event);
  const { label, type, level, parentId, status, siteId } = query;

  console.log('Query params:', { label, type, level, parentId, status, siteId });

  // 根据站点ID生成对应的回路树数据
  let treeData = generateCircuitTreeDataBySite(String(siteId || ''));

  // 根据查询条件过滤数据
  if (label && String(label).trim()) {
    const searchKeyword = String(label).toLowerCase().trim();
    console.log('Filtering circuits with keyword:', searchKeyword);

    // 递归过滤树节点
    const filterTreeNodes = (nodes: CircuitTreeNode[]): CircuitTreeNode[] => {
      return nodes.filter(node => {
        const matchesLabel = node.label.toLowerCase().includes(searchKeyword);
        const hasMatchingChildren = node.children && filterTreeNodes(node.children).length > 0;

        if (hasMatchingChildren) {
          node.children = filterTreeNodes(node.children!);
        }

        return matchesLabel || hasMatchingChildren;
      });
    };

    treeData = filterTreeNodes(treeData);
  }

  if (type && String(type).trim()) {
    console.log('Filtering by type:', type);
    // 这里可以根据类型进行过滤
  }

  if (level && Number(level) > 0) {
    console.log('Filtering by level:', level);
    // 这里可以根据层级进行过滤
  }

  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 200));

  console.log(`Generated circuit tree with ${treeData.length} root nodes`);

  return useResponseSuccess({
    items: treeData,
    total: treeData.length,
  });
});
