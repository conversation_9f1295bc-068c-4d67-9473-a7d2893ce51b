import { defineEventHandler, readBody } from 'h3';
import { useResponseSuccess } from '~/utils/response';
import { enterprises, sites, circuits } from './shared-data';

// 电力运行报表数据类型定义
interface PowerOperationData {
  // 显示字段
  circuitName: string; // 回路名称
  date: number; // 采集时间（时间戳）
  ep: number; // Ep(kWh) - 电能
  uq: number; // Uq(V) - 电压Q相
  ub: number; // Ub(V) - 电压B相
  uc: number; // Uc(V) - 电压C相
  ia: number; // Ia(A) - 电流A相
  ib: number; // Ib(A) - 电流B相
  ic: number; // Ic(A) - 电流C相
  p: number; // P(kW) - 有功功率
  q: number; // Q(kVar) - 无功功率
  pf: number; // Pf - 功率因数

  // 筛选字段（不在表格中显示，但用于过滤）
  enterpriseId: string; // 企业ID
  energyType: string; // 能源类型
  siteId: string; // 站点ID
  circuitId: string; // 回路ID
  timeInterval: string; // 时间间隔
}

/**
 * 请求参数结构
 */
interface RequestParams {
  query: {
    startTime?: string;
    endTime?: string;
    reportType?: string;
    timeInterval?: string;
    enterpriseId?: string;
    energyType?: string;
    siteId?: string;
    keyword?: string;
    circuitNames?: string[];
  };
  pagination: {
    page: number;
    pageSize: number;
  };
}

/**
 * 生成电力运行报表Mock数据
 */
function generatePowerOperationMockData(params: {
  timeInterval?: string;
  energyType?: string;
}): PowerOperationData[] {
  const {
    timeInterval = '1',
    energyType = '1',
  } = params;

  // 使用共用的企业、站点、回路数据

  const data: PowerOperationData[] = [];

  // 生成完整的基础数据（基于当前用户时间及往前30天）
  const dates: Date[] = [];
  const now = new Date();

  // 生成从今天往前30天的数据（包含今天，共31天）
  for (let dayOffset = 0; dayOffset <= 30; dayOffset++) {
    const date = new Date(now);
    date.setDate(date.getDate() - dayOffset);
    // 设置为当天的中午12:00:00
    date.setHours(12, 0, 0, 0);
    dates.push(date);
  }

  console.log('🔍 当前系统时间检查:', {
    now: now.toISOString(),
    nowDate: now.toISOString().split('T')[0],
    nowYear: now.getFullYear(),
    nowMonth: now.getMonth() + 1,
    nowDay: now.getDate()
  });

  console.log('🔍 生成数据时间范围（基于当前时间）:', {
    today: now.toISOString().split('T')[0],
    startDate: dates[dates.length - 1].toISOString().split('T')[0], // 最早日期
    endDate: dates[0].toISOString().split('T')[0], // 最晚日期（今天）
    totalDays: dates.length
  });

  dates.forEach((date, index) => {
    // 每天生成多条记录（根据日期哈希决定数量，保持一致性）
    const dateString = date.toISOString().split('T')[0];
    const dateHash = dateString.split('').reduce((hash, char) => hash + char.charCodeAt(0), 0);
    const recordsPerDay = (dateHash % 10) + 5; // 每天5-14条记录

    for (let recordIndex = 0; recordIndex < recordsPerDay; recordIndex++) {

      const enterprise = enterprises[recordIndex % enterprises.length];
      const site = sites[recordIndex % sites.length];
      const circuit = circuits[recordIndex % circuits.length];

      // 生成当天的随机时间
      const collectTime = new Date(date);
      collectTime.setHours(Math.floor(Math.random() * 24));
      collectTime.setMinutes(Math.floor(Math.random() * 60));
      collectTime.setSeconds(Math.floor(Math.random() * 60));

      // 生成基础电力数据
      const baseVoltage = 220 + Math.random() * 10 - 5; // 220V ± 5V
      const baseCurrent = 10 + Math.random() * 10; // 10-20A
      const basePower = 8 + Math.random() * 5; // 8-13kW

      data.push({
        circuitName: circuit.name,
        date: collectTime.getTime(), // 返回时间戳
        ep: Number((Math.random() * 200 + 50).toFixed(1)), // 50-250 kWh
        uq: Math.round(baseVoltage + Math.random() * 4 - 2), // 电压Q相
        ub: Math.round(baseVoltage + Math.random() * 4 - 2), // 电压B相
        uc: Math.round(baseVoltage + Math.random() * 4 - 2), // 电压C相
        ia: Number((baseCurrent + Math.random() * 2 - 1).toFixed(1)), // 电流A相
        ib: Number((baseCurrent + Math.random() * 2 - 1).toFixed(1)), // 电流B相
        ic: Number((baseCurrent + Math.random() * 2 - 1).toFixed(1)), // 电流C相
        p: Number((basePower + Math.random() * 2 - 1).toFixed(1)), // 有功功率
        q: Number((basePower * 0.6 + Math.random() * 2 - 1).toFixed(1)), // 无功功率
        pf: Number((0.8 + Math.random() * 0.15).toFixed(2)), // 功率因数 0.8-0.95

        // 筛选字段
        enterpriseId: enterprise.id,
        energyType: energyType,
        siteId: site.id,
        circuitId: circuit.id,
        timeInterval: timeInterval,
      });
    }
  });

  return data;
}

export default defineEventHandler(async (event) => {
  console.log('电力运行报表API调用 (POST)');
  console.log('🔍 服务器当前时间:', new Date().toISOString());

  // 获取POST请求体
  const requestBody: RequestParams = await readBody(event);

  const { query, pagination } = requestBody;
  const {
    startTime,
    endTime,
    reportType = 'dayly',
    timeInterval = '1',
    enterpriseId = '',
    energyType = '',
    siteId = '',
    keyword = '',
    circuitNames = []
  } = query;

  const { page = 1, pageSize = 50 } = pagination;

  console.log('🔍 查询参数:', { startTime, endTime, enterpriseId, siteId, timeInterval });

  // 生成完整的基础数据（基于当前时间及往前30天）
  let mockData = generatePowerOperationMockData({
    timeInterval: String(timeInterval),
    energyType: String(energyType),
  });

  console.log('🔍 初始数据量:', mockData.length);
  console.log('🔍 生成的数据日期范围:', {
    firstDate: mockData[0]?.date,
    lastDate: mockData[mockData.length - 1]?.date,
    sampleDates: mockData.slice(0, 5).map(item => item.date)
  });

  // 根据时间范围过滤数据
  if (startTime && endTime) {
    console.log('🔍 开始时间过滤:', {
      startTime,
      endTime,
      beforeFilterCount: mockData.length,
      sampleDates: mockData.slice(0, 5).map(item => item.date)
    });

    const start = new Date(startTime);
    const end = new Date(endTime);
    // 设置结束时间为当天的23:59:59
    end.setHours(23, 59, 59, 999);

    console.log('🔍 过滤条件:', {
      startDate: start.toISOString(),
      endDate: end.toISOString(),
      startDateOnly: start.toISOString().split('T')[0],
      endDateOnly: end.toISOString().split('T')[0]
    });

    const beforeCount = mockData.length;
    const startTimestamp = start.getTime();
    const endTimestamp = end.getTime();

    mockData = mockData.filter(item => {
      // item.date 现在是时间戳
      return item.date >= startTimestamp && item.date <= endTimestamp;
    });

    console.log('🔍 时间范围筛选结果:', {
      beforeCount,
      afterCount: mockData.length,
      filteredDates: mockData.slice(0, 3).map(item => item.date)
    });
  }

  // 根据筛选条件过滤数据
  if (enterpriseId && enterpriseId.trim()) {
    mockData = mockData.filter(item => item.enterpriseId === enterpriseId);
    console.log('🔍 企业筛选后数据量:', mockData.length);
  }

  if (siteId && siteId.trim()) {
    mockData = mockData.filter(item => item.siteId === siteId);
    console.log('🔍 站点筛选后数据量:', mockData.length);
  }

  if (keyword && keyword.trim()) {
    const searchKeyword = keyword.toLowerCase().trim();
    mockData = mockData.filter(item =>
      item.circuitName.toLowerCase().includes(searchKeyword)
    );
    console.log('🔍 关键字筛选后数据量:', mockData.length);
  }

  if (circuitNames && circuitNames.length > 0) {
    mockData = mockData.filter(item =>
      circuitNames.includes(item.circuitName)
    );
    console.log('🔍 回路筛选后数据量:', mockData.length);
  }

  // 分页处理
  const pageNum = Number(page);
  const pageSizeNum = Number(pageSize);
  const startIndex = (pageNum - 1) * pageSizeNum;
  const endIndex = startIndex + pageSizeNum;
  const paginatedData = mockData.slice(startIndex, endIndex);

  console.log('🔍 分页后数据量:', paginatedData.length);

  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 300));

  return useResponseSuccess({
    items: paginatedData,
    total: mockData.length,
    page: pageNum,
    pageSize: pageSizeNum,
    reportType,
    timeInterval: String(timeInterval),
    startTime: String(startTime || ''),
    endTime: String(endTime || ''),
  });
});
