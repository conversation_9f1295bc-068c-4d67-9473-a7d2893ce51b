import { defineEventHand<PERSON>, readBody } from 'h3';
import { useResponseSuccess } from '~/utils/response';

/**
 * 站点数据类型定义
 */
interface SiteData {
  id: string;
  name: string;
  enterpriseId: string;
  address?: string;
  description?: string;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

/**
 * 请求参数结构
 */
interface RequestParams {
  query: {
    enterpriseId?: string;
    keyword?: string;
    status?: 'active' | 'inactive';
  };
  pagination: {
    page: number;
    pageSize: number;
  };
}

/**
 * 统一的企业数据（使用真实的统一社会信用代码格式）
 */
const enterprises = [
  { id: '91110000MA001234XY', name: '华能电力集团' },
  { id: '91110000MA005678AB', name: '国家电网公司' },
  { id: '91110000MA009012CD', name: '南方电网公司' },
  { id: '91110000MA003456EF', name: '中国电建集团' },
  { id: '91110000MA007890GH', name: '大唐发电集团' },
];

/**
 * 统一的站点数据（与电力极值报表保持一致）
 */
const sites: SiteData[] = [
  {
    id: '1',
    name: '北京变电站',
    enterpriseId: '91110000MA001234XY',
    address: '北京市朝阳区电力路1号',
    description: '华能电力集团北京主变电站',
    status: 'active',
    createdAt: '2023-01-15T08:00:00Z',
    updatedAt: '2024-12-01T10:30:00Z',
  },
  {
    id: '2',
    name: '上海变电站',
    enterpriseId: '91110000MA005678AB',
    address: '上海市浦东新区电网大道88号',
    description: '国家电网公司上海分公司主变电站',
    status: 'active',
    createdAt: '2023-02-20T09:15:00Z',
    updatedAt: '2024-11-28T14:20:00Z',
  },
  {
    id: '3',
    name: '广州变电站',
    enterpriseId: '91110000MA009012CD',
    address: '广州市天河区南电路66号',
    description: '南方电网公司广州供电局变电站',
    status: 'active',
    createdAt: '2023-03-10T10:30:00Z',
    updatedAt: '2024-12-05T16:45:00Z',
  },
  {
    id: '4',
    name: '深圳变电站',
    enterpriseId: '91110000MA005678AB',
    address: '深圳市南山区科技园电力街99号',
    description: '国家电网公司深圳分公司变电站',
    status: 'active',
    createdAt: '2023-04-05T11:00:00Z',
    updatedAt: '2024-11-30T09:10:00Z',
  },
  {
    id: '5',
    name: '成都变电站',
    enterpriseId: '91110000MA003456EF',
    address: '成都市高新区电建路168号',
    description: '中国电建集团成都分公司变电站',
    status: 'active',
    createdAt: '2023-05-12T13:20:00Z',
    updatedAt: '2024-12-03T11:55:00Z',
  },
  {
    id: '6',
    name: '天津变电站',
    enterpriseId: '91110000MA001234XY',
    address: '天津市滨海新区华能路200号',
    description: '华能电力集团天津分公司变电站',
    status: 'active',
    createdAt: '2023-06-18T14:40:00Z',
    updatedAt: '2024-12-02T15:30:00Z',
  },
  {
    id: '7',
    name: '重庆变电站',
    enterpriseId: '91110000MA007890GH',
    address: '重庆市渝北区大唐街88号',
    description: '大唐发电集团重庆分公司变电站',
    status: 'active',
    createdAt: '2023-07-25T16:10:00Z',
    updatedAt: '2024-11-29T12:40:00Z',
  },
  {
    id: '8',
    name: '杭州变电站',
    enterpriseId: '91110000MA005678AB',
    address: '杭州市西湖区电网路128号',
    description: '国家电网公司杭州分公司变电站',
    status: 'active',
    createdAt: '2023-08-30T17:25:00Z',
    updatedAt: '2024-12-04T08:15:00Z',
  },
];

export default defineEventHandler(async (event) => {
  // 获取POST请求体
  const requestBody: RequestParams = await readBody(event);

  const { query, pagination } = requestBody;
  const {
    enterpriseId = '',
    keyword = '',
    status = 'active'
  } = query;

  const { page = 1, pageSize = 50 } = pagination;

  // 根据筛选条件过滤站点数据
  let filteredSites = [...sites];

  // 1. 根据企业ID筛选（必填条件）
  if (enterpriseId && enterpriseId.trim()) {
    filteredSites = filteredSites.filter(site => site.enterpriseId === enterpriseId);
  }

  // 2. 根据关键字筛选（站点名称或地址）
  if (keyword && keyword.trim()) {
    const searchKeyword = keyword.toLowerCase().trim();
    filteredSites = filteredSites.filter(site =>
      site.name.toLowerCase().includes(searchKeyword) ||
      (site.address && site.address.toLowerCase().includes(searchKeyword))
    );
  }

  // 3. 根据状态筛选
  if (status) {
    filteredSites = filteredSites.filter(site => site.status === status);
  }

  // 分页处理
  const pageNum = Number(page);
  const pageSizeNum = Number(pageSize);
  const startIndex = (pageNum - 1) * pageSizeNum;
  const endIndex = startIndex + pageSizeNum;
  const paginatedSites = filteredSites.slice(startIndex, endIndex);

  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 200));

  return useResponseSuccess({
    items: paginatedSites,
    total: filteredSites.length,
    page: pageNum,
    pageSize: pageSizeNum,
    // 调试信息
    filterConditions: {
      enterpriseId: enterpriseId || '',
      keyword: keyword || '',
      status: status || 'active',
    },
    // 企业信息（用于调试）
    enterpriseInfo: enterprises.find(e => e.id === enterpriseId) || null,
  });
});
