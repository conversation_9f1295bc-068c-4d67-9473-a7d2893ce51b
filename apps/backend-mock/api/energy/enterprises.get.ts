import { useResponseSuccess } from '~/utils/response';

// 模拟企业数据（使用统一社会信用代码格式，与其他API保持一致）
const mockEnterprises = [
  {
    id: '91110000MA001234XY',
    name: '华能电力集团',
    code: 'HNPOWER001',
    type: 'comprehensive',
    status: 'active',
    description: '大型综合性电力企业集团，业务涵盖发电、输配电等',
    createTime: '2020-01-15T08:00:00Z',
    updateTime: '2024-06-20T10:30:00Z',
  },
  {
    id: '91110000MA005678AB',
    name: '国家电网公司',
    code: 'SGCC002',
    type: 'grid',
    status: 'active',
    description: '国家级电网企业，负责电力输配和供应',
    createTime: '2019-03-22T09:15:00Z',
    updateTime: '2024-07-10T14:20:00Z',
  },
  {
    id: '91110000MA009012CD',
    name: '南方电网公司',
    code: 'CSG003',
    type: 'grid',
    status: 'active',
    description: '覆盖南方五省区的大型电网企业',
    createTime: '2021-05-10T11:30:00Z',
    updateTime: '2024-07-15T16:45:00Z',
  },
  {
    id: '91110000MA003456EF',
    name: '中国电建集团',
    code: 'POWERCHINA004',
    type: 'construction',
    status: 'active',
    description: '电力工程建设和投资运营企业',
    createTime: '2020-08-18T13:20:00Z',
    updateTime: '2024-07-12T09:10:00Z',
  },
  {
    id: '91110000MA007890GH',
    name: '大唐发电集团',
    code: 'DTPOWER005',
    type: 'generation',
    status: 'active',
    description: '大型发电企业集团，以火电、新能源发电为主',
    createTime: '2022-02-28T15:45:00Z',
    updateTime: '2024-07-18T11:25:00Z',
  },
  {
    id: '91110000MA011223IJ',
    name: '天津海上风电有限公司',
    code: 'TJHSFD006',
    type: 'wind',
    status: 'active',
    description: '专业海上风力发电项目开发运营',
    createTime: '2021-11-12T10:00:00Z',
    updateTime: '2024-07-08T13:30:00Z',
  },
  {
    id: '91110000MA014567KL',
    name: '成都分布式光伏集团',
    code: 'CDFBSGF007',
    type: 'solar',
    status: 'active',
    description: '分布式光伏发电系统集成商',
    createTime: '2020-12-05T14:15:00Z',
    updateTime: '2024-07-14T08:50:00Z',
  },
  {
    id: '91110000MA017890MN',
    name: '武汉储能技术研发中心',
    code: 'WHCNJS008',
    type: 'storage',
    status: 'active',
    description: '储能技术研发与产业化应用',
    createTime: '2021-07-20T16:30:00Z',
    updateTime: '2024-07-16T12:40:00Z',
  },
];

export default defineEventHandler(async (event) => {
  console.log('Enterprise list API called');

  // 获取查询参数
  const query = getQuery(event);
  const { keyword, type, status, page = 1, pageSize = 20 } = query;

  console.log('Query params:', { keyword, type, status, page, pageSize });

  let filteredData = [...mockEnterprises];

  // 根据关键词过滤（只有当关键词不为空且不是空字符串时才过滤）
  if (keyword && String(keyword).trim()) {
    const searchKeyword = String(keyword).toLowerCase().trim();
    console.log('Filtering enterprises with keyword:', searchKeyword);
    filteredData = filteredData.filter(
      (item) =>
        item.name.toLowerCase().includes(searchKeyword) ||
        item.code.toLowerCase().includes(searchKeyword) ||
        (item.description && item.description.toLowerCase().includes(searchKeyword))
    );
  } else {
    console.log('No keyword provided, returning all enterprises');
  }

  // 根据类型过滤
  if (type) {
    filteredData = filteredData.filter((item) => item.type === type);
  }

  // 根据状态过滤
  if (status) {
    filteredData = filteredData.filter((item) => item.status === status);
  }

  // 分页处理
  const pageNum = Number(page);
  const pageSizeNum = Number(pageSize);
  const startIndex = (pageNum - 1) * pageSizeNum;
  const endIndex = startIndex + pageSizeNum;
  const paginatedData = filteredData.slice(startIndex, endIndex);

  console.log('Filtered data length:', filteredData.length);
  console.log('Paginated data length:', paginatedData.length);

  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 200));

  return useResponseSuccess({
    items: paginatedData,
    total: filteredData.length,
    page: pageNum,
    pageSize: pageSizeNum,
  });
});
